<testsuites id="" name="" tests="20" failures="15" skipped="0" errors="0" time="46.530066999999995">
<testsuite name="auth.spec.ts" timestamp="2025-06-30T18:50:19.710Z" hostname="chromium" tests="4" failures="3" skipped="0" time="26.298" errors="0">
<testcase name="Authentication Flow › should load login page" classname="auth.spec.ts" time="1.946">
</testcase>
<testcase name="Authentication Flow › should show validation errors for empty form" classname="auth.spec.ts" time="6.789">
<failure message="auth.spec.ts:16:7 should show validation errors for empty form" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:16:7 › Authentication Flow › should show validation errors for empty form 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Username is required')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Username is required')


      21 |     
      22 |     // Should show validation errors
    > 23 |     await expect(page.locator('text=Username is required')).toBeVisible();
         |                                                             ^
      24 |     await expect(page.locator('text=Password is required')).toBeVisible();
      25 |   });
      26 |
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-chromium/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-chromium/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-chromium/video.webm is missing
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-chromium/error-context.md is missing]]>
</system-err>
</testcase>
<testcase name="Authentication Flow › should navigate to register page" classname="auth.spec.ts" time="11.25">
<failure message="auth.spec.ts:27:7 should navigate to register page" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:27:7 › Authentication Flow › should navigate to register page ──────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Register')


      29 |     
      30 |     // Click register link
    > 31 |     await page.click('text=Register');
         |                ^
      32 |     
      33 |     // Should navigate to register page
      34 |     await expect(page).toHaveURL(/\/register/);
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-chromium/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-chromium/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-chromium/video.webm is missing
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-chromium/error-context.md is missing]]>
</system-err>
</testcase>
<testcase name="Authentication Flow › should have working forgot password link" classname="auth.spec.ts" time="6.313">
<failure message="auth.spec.ts:37:7 should have working forgot password link" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:37:7 › Authentication Flow › should have working forgot password link ──

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Forgot password?')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Forgot password?')


      39 |     
      40 |     // Check if forgot password link exists
    > 41 |     await expect(page.locator('text=Forgot password?')).toBeVisible();
         |                                                         ^
      42 |   });
      43 | });
      44 |
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-chromium/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-chromium/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-chromium/video.webm is missing
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-chromium/error-context.md is missing]]>
</system-err>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-06-30T18:50:19.710Z" hostname="firefox" tests="4" failures="3" skipped="0" time="26.203" errors="0">
<testcase name="Authentication Flow › should load login page" classname="auth.spec.ts" time="2.213">
</testcase>
<testcase name="Authentication Flow › should show validation errors for empty form" classname="auth.spec.ts" time="6">
<failure message="auth.spec.ts:16:7 should show validation errors for empty form" type="FAILURE">
<![CDATA[  [firefox] › auth.spec.ts:16:7 › Authentication Flow › should show validation errors for empty form 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Username is required')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Username is required')


      21 |     
      22 |     // Should show validation errors
    > 23 |     await expect(page.locator('text=Username is required')).toBeVisible();
         |                                                             ^
      24 |     await expect(page.locator('text=Password is required')).toBeVisible();
      25 |   });
      26 |
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-firefox/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-firefox/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-firefox/video.webm is missing
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-firefox/error-context.md is missing]]>
</system-err>
</testcase>
<testcase name="Authentication Flow › should navigate to register page" classname="auth.spec.ts" time="11.349">
<failure message="auth.spec.ts:27:7 should navigate to register page" type="FAILURE">
<![CDATA[  [firefox] › auth.spec.ts:27:7 › Authentication Flow › should navigate to register page ───────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Register')


      29 |     
      30 |     // Click register link
    > 31 |     await page.click('text=Register');
         |                ^
      32 |     
      33 |     // Should navigate to register page
      34 |     await expect(page).toHaveURL(/\/register/);
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-firefox/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-firefox/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-firefox/video.webm is missing
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-firefox/error-context.md is missing]]>
</system-err>
</testcase>
<testcase name="Authentication Flow › should have working forgot password link" classname="auth.spec.ts" time="6.641">
<failure message="auth.spec.ts:37:7 should have working forgot password link" type="FAILURE">
<![CDATA[  [firefox] › auth.spec.ts:37:7 › Authentication Flow › should have working forgot password link ───

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Forgot password?')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Forgot password?')


      39 |     
      40 |     // Check if forgot password link exists
    > 41 |     await expect(page.locator('text=Forgot password?')).toBeVisible();
         |                                                         ^
      42 |   });
      43 | });
      44 |
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-firefox/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-firefox/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-firefox/video.webm is missing
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-firefox/error-context.md is missing]]>
</system-err>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-06-30T18:50:19.710Z" hostname="webkit" tests="4" failures="3" skipped="0" time="24.922" errors="0">
<testcase name="Authentication Flow › should load login page" classname="auth.spec.ts" time="1.303">
</testcase>
<testcase name="Authentication Flow › should show validation errors for empty form" classname="auth.spec.ts" time="6.517">
<failure message="auth.spec.ts:16:7 should show validation errors for empty form" type="FAILURE">
<![CDATA[  [webkit] › auth.spec.ts:16:7 › Authentication Flow › should show validation errors for empty form 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Username is required')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Username is required')


      21 |     
      22 |     // Should show validation errors
    > 23 |     await expect(page.locator('text=Username is required')).toBeVisible();
         |                                                             ^
      24 |     await expect(page.locator('text=Password is required')).toBeVisible();
      25 |   });
      26 |
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-webkit/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-webkit/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-webkit/video.webm is missing
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-webkit/error-context.md is missing]]>
</system-err>
</testcase>
<testcase name="Authentication Flow › should navigate to register page" classname="auth.spec.ts" time="11.278">
<failure message="auth.spec.ts:27:7 should navigate to register page" type="FAILURE">
<![CDATA[  [webkit] › auth.spec.ts:27:7 › Authentication Flow › should navigate to register page ────────────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Register')


      29 |     
      30 |     // Click register link
    > 31 |     await page.click('text=Register');
         |                ^
      32 |     
      33 |     // Should navigate to register page
      34 |     await expect(page).toHaveURL(/\/register/);
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-webkit/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-webkit/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-webkit/video.webm is missing
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-webkit/error-context.md is missing]]>
</system-err>
</testcase>
<testcase name="Authentication Flow › should have working forgot password link" classname="auth.spec.ts" time="5.824">
<failure message="auth.spec.ts:37:7 should have working forgot password link" type="FAILURE">
<![CDATA[  [webkit] › auth.spec.ts:37:7 › Authentication Flow › should have working forgot password link ────

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Forgot password?')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Forgot password?')


      39 |     
      40 |     // Check if forgot password link exists
    > 41 |     await expect(page.locator('text=Forgot password?')).toBeVisible();
         |                                                         ^
      42 |   });
      43 | });
      44 |
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-webkit/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-webkit/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-webkit/video.webm is missing
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-webkit/error-context.md is missing]]>
</system-err>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-06-30T18:50:19.710Z" hostname="Mobile Chrome" tests="4" failures="3" skipped="0" time="23.141" errors="0">
<testcase name="Authentication Flow › should load login page" classname="auth.spec.ts" time="0.722">
</testcase>
<testcase name="Authentication Flow › should show validation errors for empty form" classname="auth.spec.ts" time="6.058">
<failure message="auth.spec.ts:16:7 should show validation errors for empty form" type="FAILURE">
<![CDATA[  [Mobile Chrome] › auth.spec.ts:16:7 › Authentication Flow › should show validation errors for empty form 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Username is required')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Username is required')


      21 |     
      22 |     // Should show validation errors
    > 23 |     await expect(page.locator('text=Username is required')).toBeVisible();
         |                                                             ^
      24 |     await expect(page.locator('text=Password is required')).toBeVisible();
      25 |   });
      26 |
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Chrome/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Chrome/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Chrome/video.webm is missing
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Chrome/error-context.md is missing]]>
</system-err>
</testcase>
<testcase name="Authentication Flow › should navigate to register page" classname="auth.spec.ts" time="10.683">
<failure message="auth.spec.ts:27:7 should navigate to register page" type="FAILURE">
<![CDATA[  [Mobile Chrome] › auth.spec.ts:27:7 › Authentication Flow › should navigate to register page ─────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Register')


      29 |     
      30 |     // Click register link
    > 31 |     await page.click('text=Register');
         |                ^
      32 |     
      33 |     // Should navigate to register page
      34 |     await expect(page).toHaveURL(/\/register/);
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Chrome/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Chrome/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Chrome/video.webm is missing
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Chrome/error-context.md is missing]]>
</system-err>
</testcase>
<testcase name="Authentication Flow › should have working forgot password link" classname="auth.spec.ts" time="5.678">
<failure message="auth.spec.ts:37:7 should have working forgot password link" type="FAILURE">
<![CDATA[  [Mobile Chrome] › auth.spec.ts:37:7 › Authentication Flow › should have working forgot password link 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Forgot password?')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Forgot password?')


      39 |     
      40 |     // Check if forgot password link exists
    > 41 |     await expect(page.locator('text=Forgot password?')).toBeVisible();
         |                                                         ^
      42 |   });
      43 | });
      44 |
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Chrome/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Chrome/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Chrome/video.webm is missing
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Chrome/error-context.md is missing]]>
</system-err>
</testcase>
</testsuite>
<testsuite name="auth.spec.ts" timestamp="2025-06-30T18:50:19.710Z" hostname="Mobile Safari" tests="4" failures="3" skipped="0" time="24.563" errors="0">
<testcase name="Authentication Flow › should load login page" classname="auth.spec.ts" time="1.17">
</testcase>
<testcase name="Authentication Flow › should show validation errors for empty form" classname="auth.spec.ts" time="6.369">
<failure message="auth.spec.ts:16:7 should show validation errors for empty form" type="FAILURE">
<![CDATA[  [Mobile Safari] › auth.spec.ts:16:7 › Authentication Flow › should show validation errors for empty form 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Username is required')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Username is required')


      21 |     
      22 |     // Should show validation errors
    > 23 |     await expect(page.locator('text=Username is required')).toBeVisible();
         |                                                             ^
      24 |     await expect(page.locator('text=Password is required')).toBeVisible();
      25 |   });
      26 |
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Safari/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Safari/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Safari/video.webm is missing
Warning: attachment auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Safari/error-context.md is missing]]>
</system-err>
</testcase>
<testcase name="Authentication Flow › should navigate to register page" classname="auth.spec.ts" time="10.629">
<failure message="auth.spec.ts:27:7 should navigate to register page" type="FAILURE">
<![CDATA[  [Mobile Safari] › auth.spec.ts:27:7 › Authentication Flow › should navigate to register page ─────

    TimeoutError: page.click: Timeout 10000ms exceeded.
    Call log:
      - waiting for locator('text=Register')


      29 |     
      30 |     // Click register link
    > 31 |     await page.click('text=Register');
         |                ^
      32 |     
      33 |     // Should navigate to register page
      34 |     await expect(page).toHaveURL(/\/register/);
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Safari/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Safari/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Safari/video.webm is missing
Warning: attachment auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Safari/error-context.md is missing]]>
</system-err>
</testcase>
<testcase name="Authentication Flow › should have working forgot password link" classname="auth.spec.ts" time="6.395">
<failure message="auth.spec.ts:37:7 should have working forgot password link" type="FAILURE">
<![CDATA[  [Mobile Safari] › auth.spec.ts:37:7 › Authentication Flow › should have working forgot password link 

    Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

    Locator: locator('text=Forgot password?')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 5000ms
      - waiting for locator('text=Forgot password?')


      39 |     
      40 |     // Check if forgot password link exists
    > 41 |     await expect(page.locator('text=Forgot password?')).toBeVisible();
         |                                                         ^
      42 |   });
      43 | });
      44 |
        at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Safari/error-context.md
]]>
</failure>
<system-err>
<![CDATA[
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Safari/test-failed-1.png is missing
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Safari/video.webm is missing
Warning: attachment auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Safari/error-context.md is missing]]>
</system-err>
</testcase>
</testsuite>
</testsuites>