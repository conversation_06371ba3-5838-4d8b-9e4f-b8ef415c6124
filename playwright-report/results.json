{"config": {"configFile": "/Users/<USER>/Github/vocab/playwright.config.ts", "rootDir": "/Users/<USER>/Github/vocab/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Github/vocab/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Github/vocab/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "playwright-report/results.json"}], ["junit", {"outputFile": "playwright-report/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Github/vocab/playwright-report", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Github/vocab/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 4, "webServer": {"command": "yarn dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "auth.spec.ts", "file": "auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Authentication Flow", "file": "auth.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should load login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1946, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:20.233Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-a481b45c664b7b49c4c4", "file": "auth.spec.ts", "line": 4, "column": 7}, {"title": "should show validation errors for empty form", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 6789, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Should show validation errors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Username is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Password is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Should show validation errors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Username is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Password is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:20.243Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-17ab39f7a270a8bb33f9", "file": "auth.spec.ts", "line": 16, "column": 7}, {"title": "should navigate to register page", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 11250, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Click register link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'text=Register'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \n \u001b[90m 33 |\u001b[39m     \u001b[90m// Should navigate to register page\u001b[39m\n \u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/register/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Click register link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'text=Register'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \n \u001b[90m 33 |\u001b[39m     \u001b[90m// Should navigate to register page\u001b[39m\n \u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/register/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:20.261Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-77d8da85931484ac899e", "file": "auth.spec.ts", "line": 27, "column": 7}, {"title": "should have working forgot password link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 6313, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}, "snippet": "\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// Check if forgot password link exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Forgot password?'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n\n\n\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// Check if forgot password link exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Forgot password?'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:20.253Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-b7416e86346c863e3ca5", "file": "auth.spec.ts", "line": 37, "column": 7}, {"title": "should load login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "passed", "duration": 2213, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:22.891Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-dc6299ec1c95924e297a", "file": "auth.spec.ts", "line": 4, "column": 7}, {"title": "should show validation errors for empty form", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 6000, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Should show validation errors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Username is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Password is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Should show validation errors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Username is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Password is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:25.972Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-firefox/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-e1133d91878534308244", "file": "auth.spec.ts", "line": 16, "column": 7}, {"title": "should navigate to register page", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 3, "status": "failed", "duration": 11349, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Click register link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'text=Register'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \n \u001b[90m 33 |\u001b[39m     \u001b[90m// Should navigate to register page\u001b[39m\n \u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/register/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Click register link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'text=Register'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \n \u001b[90m 33 |\u001b[39m     \u001b[90m// Should navigate to register page\u001b[39m\n \u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/register/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:27.441Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-firefox/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-bec50116bc95ebb84460", "file": "auth.spec.ts", "line": 27, "column": 7}, {"title": "should have working forgot password link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 1, "status": "failed", "duration": 6641, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}, "snippet": "\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// Check if forgot password link exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Forgot password?'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n\n\n\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// Check if forgot password link exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Forgot password?'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:27.742Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-firefox/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-firefox/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-e435ff950a90a3f5c98c", "file": "auth.spec.ts", "line": 37, "column": 7}, {"title": "should load login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 7, "parallelIndex": 2, "status": "passed", "duration": 1303, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:32.155Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-6b15559c06f19d0369e4", "file": "auth.spec.ts", "line": 4, "column": 7}, {"title": "should show validation errors for empty form", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 8, "parallelIndex": 0, "status": "failed", "duration": 6517, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Should show validation errors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Username is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Password is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Should show validation errors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Username is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Password is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:32.900Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-webkit/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-bb4afae2d711af588f60", "file": "auth.spec.ts", "line": 16, "column": 7}, {"title": "should navigate to register page", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 7, "parallelIndex": 2, "status": "failed", "duration": 11278, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Click register link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'text=Register'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \n \u001b[90m 33 |\u001b[39m     \u001b[90m// Should navigate to register page\u001b[39m\n \u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/register/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Click register link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'text=Register'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \n \u001b[90m 33 |\u001b[39m     \u001b[90m// Should navigate to register page\u001b[39m\n \u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/register/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:33.739Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-webkit/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-b909b68fe3ffdd8ade22", "file": "auth.spec.ts", "line": 27, "column": 7}, {"title": "should have working forgot password link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 9, "parallelIndex": 1, "status": "failed", "duration": 5824, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}, "snippet": "\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// Check if forgot password link exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Forgot password?'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n\n\n\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// Check if forgot password link exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Forgot password?'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:36.053Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-webkit/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-webkit/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-4ac139fb36cd94df54b2", "file": "auth.spec.ts", "line": 37, "column": 7}, {"title": "should load login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "passed", "duration": 722, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:40.123Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-499542535e24b85c6de2", "file": "auth.spec.ts", "line": 4, "column": 7}, {"title": "should show validation errors for empty form", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "failed", "duration": 6058, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Should show validation errors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Username is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Password is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Should show validation errors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Username is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Password is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:40.294Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-465243df483e808a6107", "file": "auth.spec.ts", "line": 16, "column": 7}, {"title": "should navigate to register page", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "failed", "duration": 10683, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Click register link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'text=Register'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \n \u001b[90m 33 |\u001b[39m     \u001b[90m// Should navigate to register page\u001b[39m\n \u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/register/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Click register link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'text=Register'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \n \u001b[90m 33 |\u001b[39m     \u001b[90m// Should navigate to register page\u001b[39m\n \u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/register/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:41.065Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-befd1c7fd3245babc914", "file": "auth.spec.ts", "line": 27, "column": 7}, {"title": "should have working forgot password link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 12, "parallelIndex": 1, "status": "failed", "duration": 5678, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}, "snippet": "\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// Check if forgot password link exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Forgot password?'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n\n\n\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// Check if forgot password link exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Forgot password?'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:42.612Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-6dafcbec6d8dd9c49b2b", "file": "auth.spec.ts", "line": 37, "column": 7}, {"title": "should load login page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 13, "parallelIndex": 2, "status": "passed", "duration": 1170, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:45.479Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-af148ed8a597cd17eacc", "file": "auth.spec.ts", "line": 4, "column": 7}, {"title": "should show validation errors for empty form", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 14, "parallelIndex": 3, "status": "failed", "duration": 6369, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}, "snippet": "\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Should show validation errors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Username is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Password is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Username is required')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Username is required')\u001b[22m\n\n\n\u001b[0m \u001b[90m 21 |\u001b[39m     \n \u001b[90m 22 |\u001b[39m     \u001b[90m// Should show validation errors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 23 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Username is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 24 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Password is required'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 25 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 26 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:23:61\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:47.137Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Safari/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-fa38e-ation-errors-for-empty-form-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 61, "line": 23}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-bd5f9991c92ddd1490ed", "file": "auth.spec.ts", "line": 16, "column": 7}, {"title": "should navigate to register page", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 13, "parallelIndex": 2, "status": "failed", "duration": 10629, "error": {"message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n", "stack": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}, "snippet": "\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Click register link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'text=Register'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \n \u001b[90m 33 |\u001b[39m     \u001b[90m// Should navigate to register page\u001b[39m\n \u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/register/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}, "message": "TimeoutError: page.click: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Register')\u001b[22m\n\n\n\u001b[0m \u001b[90m 29 |\u001b[39m     \n \u001b[90m 30 |\u001b[39m     \u001b[90m// Click register link\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'text=Register'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 32 |\u001b[39m     \n \u001b[90m 33 |\u001b[39m     \u001b[90m// Should navigate to register page\u001b[39m\n \u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveURL(\u001b[35m/\\/register/\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:31:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:46.864Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Safari/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-should-navigate-to-register-page-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 16, "line": 31}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-6b7605296d452143da06", "file": "auth.spec.ts", "line": 27, "column": 7}, {"title": "should have working forgot password link", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 15, "parallelIndex": 1, "status": "failed", "duration": 6395, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n\n    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57", "location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}, "snippet": "\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// Check if forgot password link exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Forgot password?'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('text=Forgot password?')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('text=Forgot password?')\u001b[22m\n\n\n\u001b[0m \u001b[90m 39 |\u001b[39m     \n \u001b[90m 40 |\u001b[39m     \u001b[90m// Check if forgot password link exists\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 41 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'text=Forgot password?'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 42 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 43 |\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m 44 |\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Github/vocab/e2e/auth.spec.ts:41:57\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-30T18:50:49.784Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Safari/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Users/<USER>/Github/vocab/playwright-report/auth-Authentication-Flow-s-916cb-orking-forgot-password-link-Mobile-Safari/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Github/vocab/e2e/auth.spec.ts", "column": 57, "line": 41}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-5d62e4480f1869d82a2d", "file": "auth.spec.ts", "line": 37, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-06-30T18:50:11.052Z", "duration": 46530.066999999995, "expected": 5, "skipped": 0, "unexpected": 15, "flaky": 0}}