import { useCallback, useState } from 'react';
import { Difficulty } from '@prisma/client';
import { GrammarPracticeResultItem } from '@/backend/services';
import { useKeywordsContext, useLLM } from '@/contexts';
import { PARAGRAPH_COUNT } from './constants';
import type { SelectedWord, ErrorDensity, GenerationState } from './types';

export const useGrammarPractice = (collection: any) => {
	const { generateGrammarPractice, isLoading: isLLMLoading, error: llmError } = useLLM();
	const [paragraphs, setParagraphs] = useState<GrammarPracticeResultItem[]>([]);
	const [selections, setSelections] = useState<Record<number, SelectedWord[]>>({});
	const [generationState, setGenerationState] = useState<GenerationState>({
		loading: false,
		error: null,
		success: false,
	});
	const { selectedKeywords, setSelectedKeywords } = useKeywordsContext();
	const [difficulty, setDifficulty] = useState<Difficulty>('BEGINNER');
	const [errorDensity, setErrorDensity] = useState<ErrorDensity>('medium');

	// Combined loading state
	const isLoading = generationState.loading || isLLMLoading;

	const generateParagraphs = useCallback(async () => {
		// Validate initial state before starting
		setGenerationState({ loading: true, error: null, success: false });

		try {
			// Input validation
			if (!selectedKeywords.length) throw new Error('No keywords selected');
			if (!collection) throw new Error('Collection not found');

			// Reset states
			setParagraphs([]);
			setSelections({});

			// Generate practice content
			const result = await generateGrammarPractice({
				keywords: selectedKeywords,
				language: collection.target_language || 'EN',
				source_language: collection.source_language || 'VI',
				target_language: collection.target_language || 'EN',
				difficulty: difficulty as any,
				count: PARAGRAPH_COUNT,
				errorDensity,
			});

			// Validate result
			if (!result?.length) {
				throw new Error('No paragraphs generated');
			}

			// Update state with successful result
			setParagraphs(result);
			setGenerationState({ loading: false, error: null, success: true });
		} catch (error) {
			// Handle errors consistently
			const errorMessage = error instanceof Error ? error.message : String(error);
			setGenerationState({
				loading: false,
				error: new Error(errorMessage),
				success: false,
			});
			setParagraphs([]);
		}
	}, [selectedKeywords, difficulty, errorDensity, generateGrammarPractice, collection]);

	const toggleWordSelection = useCallback(
		(paragraphIndex: number, word: string, wordIndex: number) => {
			setSelections((prev) => {
				const currentSelections = prev[paragraphIndex] || [];
				const exists = currentSelections.some((s) => s.index === wordIndex);
				const newSelections = exists
					? currentSelections.filter((s) => s.index !== wordIndex)
					: [...currentSelections, { word, index: wordIndex }];

				// Update progress
				const isCorrect = paragraphs[paragraphIndex]?.allErrors.some(
					(err) => err.errorText === word
				);

				return {
					...prev,
					[paragraphIndex]: newSelections,
				};
			});
		},
		[paragraphs]
	);

	return {
		paragraphs,
		selections,
		generationState,
		isLoading,
		llmError,
		selectedKeywords,
		setSelectedKeywords,
		difficulty,
		setDifficulty,
		errorDensity,
		setErrorDensity,
		generateParagraphs,
		toggleWordSelection,
		hasError: !!(generationState.error || llmError),
		errorMessage: (generationState.error || llmError)?.message,
	};
};
