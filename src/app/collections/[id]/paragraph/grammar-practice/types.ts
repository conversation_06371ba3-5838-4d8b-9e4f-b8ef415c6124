// Types for Grammar Practice functionality
export type SelectedWord = {
	word: string;
	index: number;
};

export type ErrorDensity = 'low' | 'medium' | 'high';

export type GenerationState = {
	loading: boolean;
	error: Error | null;
	success: boolean;
};

export type Progress = {
	current: number;
	total: number;
	correct: number;
};

// Error classification types
export type ErrorDifficulty = 'easy' | 'medium' | 'hard' | 'very_hard';

export type ErrorType = {
	id: string;
	name: string;
	example: string;
	difficulty: ErrorDifficulty;
	description: string;
	category: 'grammar' | 'vocabulary' | 'mechanics' | 'style';
};

export type ErrorCategory = {
	id: string;
	name: string;
	description: string;
	color: string;
	icon: string;
	errors: ErrorType[];
};
