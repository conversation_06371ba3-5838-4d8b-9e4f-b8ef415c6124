import {
	trackWordReviewApi,
	trackQAPracticeSubmissionApi,
	trackParagraphPracticeSubmissionApi,
} from '@/backend/api/collection-stats.api';
import { auth } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
	try {
		const { id: collectionId } = await params;
		const session = await auth();

		if (!session?.user?.id) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();
		const { type, count = 1 } = body;

		if (!type || !['word_review', 'qa_practice', 'paragraph_practice'].includes(type)) {
			return NextResponse.json(
				{
					error: 'Invalid tracking type. Must be one of: word_review, qa_practice, paragraph_practice',
				},
				{ status: 400 }
			);
		}

		switch (type) {
			case 'word_review':
				await trackWordReview<PERSON>pi(collectionId, session.user.id, count);
				break;
			case 'qa_practice':
				await trackQAPracticeSubmissionApi(collectionId, session.user.id, count);
				break;
			case 'paragraph_practice':
				await trackParagraphPracticeSubmissionApi(collectionId, session.user.id, count);
				break;
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error('Error tracking stats:', error);
		return NextResponse.json({ error: 'Failed to track stats' }, { status: 500 });
	}
}
