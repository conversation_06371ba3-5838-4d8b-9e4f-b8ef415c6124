import { providerLoginApi } from '@/backend/api';
import { ValidationError, UnauthorizedError } from '@/backend/errors';
import { getFeatureFlags } from '@/config';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		// Check if Google login feature is enabled
		const featureFlags = await getFeatureFlags();
		if (!featureFlags.googleLogin) {
			return NextResponse.json({ error: 'Google login is not enabled' }, { status: 403 });
		}

		const body = await request.json();
		const { google_id } = body;

		if (!google_id) {
			return NextResponse.json({ error: 'Google ID is required' }, { status: 400 });
		}

		// Use Google ID as provider_id for Google OAuth
		await providerLoginApi('GOOGLE' as any, google_id);

		return NextResponse.json({
			success: true,
			message: 'Google login successful',
		});
	} catch (error) {
		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Google login error:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
