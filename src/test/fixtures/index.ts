import { PrismaClient, Language, Difficulty, Provider, PartsOfSpeech } from '@prisma/client';
import { WordDetail } from '@/models/word';

export async function createTestUser(prisma: PrismaClient, overrides: any = {}) {
  return prisma.user.create({
    data: {
      provider: Provider.USERNAME_PASSWORD,
      provider_id: `test-${Date.now()}-${Math.random()}`,
      username: `testuser-${Date.now()}-${Math.random()}`,
      password_hash: '$2a$10$test.hash.for.testing.purposes.only',
      ...overrides,
    },
  });
}

export async function createTestCollection(prisma: PrismaClient, userId: string, overrides: any = {}) {
  return prisma.collection.create({
    data: {
      name: `Test Collection ${Date.now()}`,
      target_language: Language.EN,
      source_language: Language.VI,
      user_id: userId,
      word_ids: [],
      paragraph_ids: [],
      keyword_ids: [],
      ...overrides,
    },
  });
}

export async function createTestWord(prisma: PrismaClient, overrides: any = {}) {
  return prisma.word.create({
    data: {
      term: `testword-${Date.now()}-${Math.random()}`,
      language: Language.EN,
      definitions: {
        create: {
          pos: [PartsOfSpeech.NOUN],
          ipa: '/test/',
          explains: {
            create: {
              EN: 'Test explanation',
              VI: 'Giải thích test',
            },
          },
          examples: {
            create: {
              EN: 'Test example',
              VI: 'Ví dụ test',
            },
          },
        },
      },
      ...overrides,
    },
    include: {
      definitions: {
        include: {
          explains: true,
          examples: true,
        },
      },
    },
  });
}

export async function createTestParagraph(prisma: PrismaClient, overrides: any = {}) {
  return prisma.paragraph.create({
    data: {
      content: 'This is a test paragraph for testing purposes.',
      difficulty: Difficulty.INTERMEDIATE,
      language: Language.EN,
      length: 'MEDIUM',
      multiple_choice_exercises: {
        create: [
          {
            question: 'What is this paragraph about?',
            options: ['Testing', 'Learning', 'Programming', 'Writing'],
            answer: 0,
            explanation: 'The paragraph is about testing.',
          },
        ],
      },
      ...overrides,
    },
    include: {
      multiple_choice_exercises: true,
    },
  });
}

export async function createTestKeyword(prisma: PrismaClient, userId: string, overrides: any = {}) {
  return prisma.keyword.create({
    data: {
      content: `test-keyword-${Date.now()}`,
      user_id: userId,
      ...overrides,
    },
  });
}

export async function createTestLastSeenWord(prisma: PrismaClient, userId: string, wordId: string, overrides: any = {}) {
  return prisma.lastSeenWord.create({
    data: {
      user_id: userId,
      word_id: wordId,
      last_seen_at: new Date(),
      review_count: 1,
      ...overrides,
    },
  });
}

export async function createTestFeedback(prisma: PrismaClient, userId: string, overrides: any = {}) {
  return prisma.feedback.create({
    data: {
      message: 'This is test feedback',
      user_id: userId,
      ...overrides,
    },
  });
}

export async function createTestCollectionStats(prisma: PrismaClient, collectionId: string, userId: string, overrides: any = {}) {
  return prisma.collectionStats.create({
    data: {
      collection_id: collectionId,
      user_id: userId,
      date: new Date(),
      words_reviewed_count: 5,
      qa_practice_submissions: 2,
      paragraph_practice_submissions: 1,
      ...overrides,
    },
  });
}

// Mock data generators
export function mockWordDetail(overrides: Partial<WordDetail> = {}): WordDetail {
  return {
    id: 'mock-word-id',
    term: 'test',
    language: Language.EN,
    audio_url: null,
    created_at: new Date(),
    updated_at: new Date(),
    definitions: [
      {
        id: 'mock-definition-id',
        word_id: 'mock-word-id',
        pos: [PartsOfSpeech.NOUN],
        ipa: '/test/',
        images: [],
        explains: [
          {
            id: 'mock-explain-id',
            EN: 'Test explanation',
            VI: 'Giải thích test',
            definition_id: 'mock-definition-id',
          },
        ],
        examples: [
          {
            id: 'mock-example-id',
            EN: 'Test example',
            VI: 'Ví dụ test',
            definition_id: 'mock-definition-id',
          },
        ],
      },
    ],
    ...overrides,
  };
}

export function mockUser(overrides: any = {}) {
  return {
    id: 'mock-user-id',
    provider: Provider.USERNAME_PASSWORD,
    provider_id: 'mock-provider-id',
    username: 'testuser',
    password_hash: '$2a$10$test.hash',
    disabled: false,
    created_at: new Date(),
    updated_at: new Date(),
    ...overrides,
  };
}

export function mockCollection(overrides: any = {}) {
  return {
    id: 'mock-collection-id',
    name: 'Test Collection',
    target_language: Language.EN,
    source_language: Language.VI,
    user_id: 'mock-user-id',
    word_ids: [],
    paragraph_ids: [],
    keyword_ids: [],
    enable_learn_word_notification: false,
    created_at: new Date(),
    updated_at: new Date(),
    ...overrides,
  };
}
