import '@testing-library/jest-dom';
import React from 'react';

// Extend Jest matchers
expect.extend({});

// Mock Next.js router
jest.mock('next/navigation', () => ({
	useRouter: () => ({
		push: jest.fn(),
		replace: jest.fn(),
		back: jest.fn(),
		forward: jest.fn(),
		refresh: jest.fn(),
		prefetch: jest.fn(),
	}),
	useSearchParams: () => new URLSearchParams(),
	usePathname: () => '/',
	useParams: () => ({}),
	notFound: jest.fn(),
	redirect: jest.fn(),
}));

// Mock Next.js Image component
jest.mock('next/image', () => ({
	__esModule: true,
	default: (props: any) => {
		const { src, alt, ...rest } = props;
		// eslint-disable-next-line @next/next/no-img-element
		return React.createElement('img', { src, alt, ...rest });
	},
}));

// Mock Next.js Link component
jest.mock('next/link', () => ({
	__esModule: true,
	default: ({ children, href, ...props }: any) => {
		return React.createElement('a', { href, ...props }, children);
	},
}));

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/vocab_test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.LLM_OPENAI_API_KEY = 'test-openai-key';

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
	writable: true,
	value: jest.fn().mockImplementation((query) => ({
		matches: false,
		media: query,
		onchange: null,
		addListener: jest.fn(), // deprecated
		removeListener: jest.fn(), // deprecated
		addEventListener: jest.fn(),
		removeEventListener: jest.fn(),
		dispatchEvent: jest.fn(),
	})),
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
	constructor() {}
	observe() {
		return null;
	}
	disconnect() {
		return null;
	}
	unobserve() {
		return null;
	}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
	constructor() {}
	observe() {
		return null;
	}
	disconnect() {
		return null;
	}
	unobserve() {
		return null;
	}
};

// Mock localStorage
const localStorageMock = {
	getItem: jest.fn(),
	setItem: jest.fn(),
	removeItem: jest.fn(),
	clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
	getItem: jest.fn(),
	setItem: jest.fn(),
	removeItem: jest.fn(),
	clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Test cleanup
afterEach(() => {
	// Clear all mocks
	jest.clearAllMocks();

	// Clear localStorage and sessionStorage
	localStorageMock.clear();
	sessionStorageMock.clear();
});

// Suppress console errors in tests unless explicitly needed
const originalError = console.error;
beforeAll(() => {
	console.error = (...args: any[]) => {
		if (
			typeof args[0] === 'string' &&
			args[0].includes('Warning: ReactDOM.render is no longer supported')
		) {
			return;
		}
		originalError.call(console, ...args);
	};
});

afterAll(() => {
	console.error = originalError;
});
