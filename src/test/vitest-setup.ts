import '@testing-library/jest-dom';
import React from 'react';
import { vi } from 'vitest';

// Extend expect matchers
expect.extend({});

// Mock Next.js router
vi.mock('next/navigation', () => ({
	useRouter: () => ({
		push: vi.fn(),
		replace: vi.fn(),
		back: vi.fn(),
		forward: vi.fn(),
		refresh: vi.fn(),
		prefetch: vi.fn(),
	}),
	useSearchParams: () => new URLSearchParams(),
	usePathname: () => '/',
	useParams: () => ({}),
	notFound: vi.fn(),
	redirect: vi.fn(),
}));

// Mock Next.js Image component
vi.mock('next/image', () => ({
	__esModule: true,
	default: (props: any) => {
		const { src, alt, ...rest } = props;
		// eslint-disable-next-line @next/next/no-img-element
		return React.createElement('img', { src, alt, ...rest });
	},
}));

// Mock Next.js Link component
vi.mock('next/link', () => ({
	__esModule: true,
	default: ({ children, href, ...props }: any) => {
		return React.createElement('a', { href, ...props }, children);
	},
}));

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/vocab_test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.LLM_OPENAI_API_KEY = 'test-openai-key';

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
	writable: true,
	value: vi.fn().mockImplementation((query) => ({
		matches: false,
		media: query,
		onchange: null,
		addListener: vi.fn(), // deprecated
		removeListener: vi.fn(), // deprecated
		addEventListener: vi.fn(),
		removeEventListener: vi.fn(),
		dispatchEvent: vi.fn(),
	})),
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
	constructor() {}
	observe() {
		return null;
	}
	disconnect() {
		return null;
	}
	unobserve() {
		return null;
	}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
	constructor() {}
	observe() {
		return null;
	}
	disconnect() {
		return null;
	}
	unobserve() {
		return null;
	}
};

// Mock localStorage
const localStorageMock = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: vi.fn((key: string) => store[key] || null),
		setItem: vi.fn((key: string, value: string) => {
			store[key] = value;
		}),
		removeItem: vi.fn((key: string) => {
			delete store[key];
		}),
		clear: vi.fn(() => {
			store = {};
		}),
	};
})();
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: vi.fn((key: string) => store[key] || null),
		setItem: vi.fn((key: string, value: string) => {
			store[key] = value;
		}),
		removeItem: vi.fn((key: string) => {
			delete store[key];
		}),
		clear: vi.fn(() => {
			store = {};
		}),
	};
})();
global.sessionStorage = sessionStorageMock;

// Test cleanup
afterEach(() => {
	// Clear all mocks
	vi.clearAllMocks();

	// Clear localStorage and sessionStorage
	localStorageMock.clear();
	sessionStorageMock.clear();
});

// Suppress console errors in tests unless explicitly needed
const originalError = console.error;
beforeAll(() => {
	console.error = (...args: any[]) => {
		if (
			typeof args[0] === 'string' &&
			args[0].includes('Warning: ReactDOM.render is no longer supported')
		) {
			return;
		}
		originalError.call(console, ...args);
	};
});

afterAll(() => {
	console.error = originalError;
});
