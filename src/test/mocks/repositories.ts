import { vi } from 'vitest';
import { WordRepository } from '@/backend/repositories/word.repository';
import { CollectionRepository } from '@/backend/repositories/collection.repository';
import { UserRepository } from '@/backend/repositories/user.repository';
import { LastSeenWordRepository } from '@/backend/repositories/last-seen-word.repository';
import { ParagraphRepository } from '@/backend/repositories/paragraph.repository';
import { FeedbackRepository } from '@/backend/repositories/feedback.repository';
import { CollectionStatsRepository } from '@/backend/repositories/collection-stats.repository';

// Mock Word Repository
export const mockWordRepository = (): jest.Mocked<WordRepository> => ({
	find: vi.fn(),
	findById: vi.fn(),
	create: vi.fn(),
	update: vi.fn(),
	delete: vi.fn(),
	findOne: vi.fn(),
	searchWords: vi.fn(),
	findWordsByIds: vi.fn(),
	findByLanguage: vi.fn(),
	findByTerm: vi.fn(),
	createMany: vi.fn(),
	findWithDefinitions: vi.fn(),
});

// Mock Collection Repository
export const mockCollectionRepository = (): jest.Mocked<CollectionRepository> => ({
	find: vi.fn(),
	findById: vi.fn(),
	create: vi.fn(),
	update: vi.fn(),
	delete: vi.fn(),
	findOne: vi.fn(),
	findByUserId: vi.fn(),
	findByUserIdAndId: vi.fn(),
	updateWordIds: vi.fn(),
	updateParagraphIds: vi.fn(),
	updateKeywordIds: vi.fn(),
	findWithStats: vi.fn(),
});

// Mock User Repository
export const mockUserRepository = (): jest.Mocked<UserRepository> => ({
	find: vi.fn(),
	findById: vi.fn(),
	create: vi.fn(),
	update: vi.fn(),
	delete: vi.fn(),
	findByProvider: vi.fn(),
	findByUsername: vi.fn(),
	findByProviderId: vi.fn(),
	updatePassword: vi.fn(),
	disable: vi.fn(),
	enable: vi.fn(),
});

// Mock LastSeenWord Repository
export const mockLastSeenWordRepository = (): jest.Mocked<LastSeenWordRepository> => ({
	find: vi.fn(),
	findById: vi.fn(),
	create: vi.fn(),
	update: vi.fn(),
	delete: vi.fn(),
	findByUserId: vi.fn(),
	findByUserIdAndWordId: vi.fn(),
	updateLastSeen: vi.fn(),
	incrementReviewCount: vi.fn(),
	findOldestReviewed: vi.fn(),
	findRecentlyReviewed: vi.fn(),
});

// Mock Paragraph Repository
export const mockParagraphRepository = (): jest.Mocked<ParagraphRepository> => ({
	find: vi.fn(),
	findById: vi.fn(),
	create: vi.fn(),
	update: vi.fn(),
	delete: vi.fn(),
	findByLanguage: vi.fn(),
	findByDifficulty: vi.fn(),
	findByIds: vi.fn(),
	createMany: vi.fn(),
	findWithExercises: vi.fn(),
});

// Mock Feedback Repository
export const mockFeedbackRepository = (): jest.Mocked<FeedbackRepository> => ({
	find: vi.fn(),
	findById: vi.fn(),
	create: vi.fn(),
	update: vi.fn(),
	delete: vi.fn(),
	findByUserId: vi.fn(),
	findRecent: vi.fn(),
	markAsRead: vi.fn(),
});

// Mock CollectionStats Repository
export const mockCollectionStatsRepository = (): jest.Mocked<CollectionStatsRepository> => ({
	find: vi.fn(),
	findById: vi.fn(),
	create: vi.fn(),
	update: vi.fn(),
	delete: vi.fn(),
	findByCollectionId: vi.fn(),
	findByUserIdAndDate: vi.fn(),
	incrementWordsReviewed: vi.fn(),
	incrementQAPractice: vi.fn(),
	incrementParagraphPractice: vi.fn(),
	findByDateRange: vi.fn(),
	getCollectionStats: vi.fn(),
});
