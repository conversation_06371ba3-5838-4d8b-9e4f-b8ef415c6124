import { CollectionStats, PrismaClient } from '@prisma/client';

export interface CollectionStatsRepository {
	getStatsByCollectionAndDateRange(
		collectionId: string,
		userId: string,
		startDate: Date,
		endDate: Date
	): Promise<CollectionStats[]>;
	
	getStatsByCollectionAndDate(
		collectionId: string,
		userId: string,
		date: Date
	): Promise<CollectionStats | null>;
	
	incrementWordsReviewed(
		collectionId: string,
		userId: string,
		date: Date,
		count?: number
	): Promise<CollectionStats>;
	
	incrementQAPracticeSubmissions(
		collectionId: string,
		userId: string,
		date: Date,
		count?: number
	): Promise<CollectionStats>;
	
	incrementParagraphPracticeSubmissions(
		collectionId: string,
		userId: string,
		date: Date,
		count?: number
	): Promise<CollectionStats>;
	
	createOrUpdateStats(
		collectionId: string,
		userId: string,
		date: Date,
		updates: Partial<Pick<CollectionStats, 'words_reviewed_count' | 'qa_practice_submissions' | 'paragraph_practice_submissions'>>
	): Promise<CollectionStats>;
}

export class CollectionStatsRepositoryImpl implements CollectionStatsRepository {
	constructor(private readonly prisma: PrismaClient) {}

	async getStatsByCollectionAndDateRange(
		collectionId: string,
		userId: string,
		startDate: Date,
		endDate: Date
	): Promise<CollectionStats[]> {
		return this.prisma.collectionStats.findMany({
			where: {
				collection_id: collectionId,
				user_id: userId,
				date: {
					gte: startDate,
					lte: endDate,
				},
			},
			orderBy: {
				date: 'asc',
			},
		});
	}

	async getStatsByCollectionAndDate(
		collectionId: string,
		userId: string,
		date: Date
	): Promise<CollectionStats | null> {
		// Normalize date to start of day
		const normalizedDate = new Date(date);
		normalizedDate.setHours(0, 0, 0, 0);

		return this.prisma.collectionStats.findUnique({
			where: {
				collection_id_user_id_date: {
					collection_id: collectionId,
					user_id: userId,
					date: normalizedDate,
				},
			},
		});
	}

	async incrementWordsReviewed(
		collectionId: string,
		userId: string,
		date: Date,
		count: number = 1
	): Promise<CollectionStats> {
		return this.createOrUpdateStats(collectionId, userId, date, {
			words_reviewed_count: count,
		});
	}

	async incrementQAPracticeSubmissions(
		collectionId: string,
		userId: string,
		date: Date,
		count: number = 1
	): Promise<CollectionStats> {
		return this.createOrUpdateStats(collectionId, userId, date, {
			qa_practice_submissions: count,
		});
	}

	async incrementParagraphPracticeSubmissions(
		collectionId: string,
		userId: string,
		date: Date,
		count: number = 1
	): Promise<CollectionStats> {
		return this.createOrUpdateStats(collectionId, userId, date, {
			paragraph_practice_submissions: count,
		});
	}

	async createOrUpdateStats(
		collectionId: string,
		userId: string,
		date: Date,
		updates: Partial<Pick<CollectionStats, 'words_reviewed_count' | 'qa_practice_submissions' | 'paragraph_practice_submissions'>>
	): Promise<CollectionStats> {
		// Normalize date to start of day
		const normalizedDate = new Date(date);
		normalizedDate.setHours(0, 0, 0, 0);

		const updateData: any = {};
		if (updates.words_reviewed_count !== undefined) {
			updateData.words_reviewed_count = { increment: updates.words_reviewed_count };
		}
		if (updates.qa_practice_submissions !== undefined) {
			updateData.qa_practice_submissions = { increment: updates.qa_practice_submissions };
		}
		if (updates.paragraph_practice_submissions !== undefined) {
			updateData.paragraph_practice_submissions = { increment: updates.paragraph_practice_submissions };
		}

		return this.prisma.collectionStats.upsert({
			where: {
				collection_id_user_id_date: {
					collection_id: collectionId,
					user_id: userId,
					date: normalizedDate,
				},
			},
			update: updateData,
			create: {
				collection_id: collectionId,
				user_id: userId,
				date: normalizedDate,
				words_reviewed_count: updates.words_reviewed_count || 0,
				qa_practice_submissions: updates.qa_practice_submissions || 0,
				paragraph_practice_submissions: updates.paragraph_practice_submissions || 0,
			},
		});
	}
}
