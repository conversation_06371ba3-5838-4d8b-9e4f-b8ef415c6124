import { LastSeenWordRepository } from '@/backend/repositories';
import { LastSeenWord } from '@prisma/client';
import { WordDetail } from '@/models';

// Enrich model for LastSeenWord
export type LastSeenWordWithWord = LastSeenWord & {
	word?: WordDetail;
};

export interface LastSeenWordService {
	saveLastSeenWord(userId: string, wordId: string): Promise<void>;
	findLastSeenWordsByWordIds(userId: string, wordIds: string[]): Promise<LastSeenWordWithWord[]>;
}

export class LastSeenWordServiceImpl implements LastSeenWordService {
	constructor(private readonly getLastSeenWordRepository: () => LastSeenWordRepository) {}

	async saveLastSeenWord(userId: string, wordId: string): Promise<void> {
		try {
			if (!userId || !wordId) {
				throw new Error('User ID and Word ID are required');
			}

			const existing = await this.getLastSeenWordRepository().findLastSeenWord(userId, wordId);

			if (existing) {
				await this.getLastSeenWordRepository().update(existing.id, {
					last_seen_at: new Date(),
				});
			} else {
				await this.getLastSeenWordRepository().create({
					user_id: userId,
					word_id: wordId,
					last_seen_at: new Date(),
				});
			}
		} catch (error) {
			console.error('Error saving last seen word:', error);
			throw new Error(
				`Failed to save last seen word: ${error instanceof Error ? error.message : 'Unknown error'}`
			);
		}
	}

	async findLastSeenWordsByWordIds(userId: string, wordIds: string[]): Promise<LastSeenWordWithWord[]> {
		try {
			if (!userId) {
				throw new Error('User ID is required');
			}

			if (!wordIds || !Array.isArray(wordIds) || wordIds.length === 0) {
				return [];
			}

			const lastSeenWords = await this.getLastSeenWordRepository().findLastSeenWordsByWordIds(userId, wordIds);
			// TODO: Enrich with WordDetail if needed (e.g., join with WordService)
			return lastSeenWords as LastSeenWordWithWord[];
		} catch (error) {
			console.error('Error finding last seen words:', error);
			throw new Error(
				`Failed to find last seen words: ${error instanceof Error ? error.message : 'Unknown error'}`
			);
		}
	}
}
