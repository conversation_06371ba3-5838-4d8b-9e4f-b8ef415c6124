import { describe, it, expect, beforeEach, vi } from 'vitest';
import { CollectionServiceImpl } from '../collection.service';
import { mockCollectionRepository } from '@/test/mocks/repositories';
import { Language } from '@prisma/client';
import { Collection } from '@/models/collection';

describe('CollectionService', () => {
	let collectionService: CollectionServiceImpl;
	let mockRepository: ReturnType<typeof mockCollectionRepository>;
	let mockGetCollectionRepository: () => any;
	let mockGetLLMService: () => any;
	let mockGetWordService: () => any;

	beforeEach(() => {
		mockRepository = mockCollectionRepository();
		mockGetCollectionRepository = vi.fn(() => mockRepository);
		mockGetLLMService = vi.fn(() => ({}));
		mockGetWordService = vi.fn(() => ({}));

		collectionService = new CollectionServiceImpl(
			mockGetCollectionRepository,
			mockGetLLMService,
			mockGetWordService
		);
	});

	describe('getUserCollections', () => {
		it('should return user collections', async () => {
			const mockCollections: Collection[] = [
				{
					id: '1',
					name: 'Test Collection 1',
					target_language: Language.EN,
					source_language: Language.VI,
					user_id: 'user-1',
					word_ids: ['word-1', 'word-2'],
					paragraph_ids: [],
					keyword_ids: [],
					enable_learn_word_notification: false,
					created_at: new Date(),
					updated_at: new Date(),
				},
				{
					id: '2',
					name: 'Test Collection 2',
					target_language: Language.VI,
					source_language: Language.EN,
					user_id: 'user-1',
					word_ids: [],
					paragraph_ids: ['para-1'],
					keyword_ids: [],
					enable_learn_word_notification: true,
					created_at: new Date(),
					updated_at: new Date(),
				},
			];

			mockRepository.findByUserId.mockResolvedValue(mockCollections);

			const result = await collectionService.getUserCollections('user-1');

			expect(result).toEqual(mockCollections);
			expect(mockRepository.findByUserId).toHaveBeenCalledWith('user-1');
		});

		it('should return empty array for user with no collections', async () => {
			mockRepository.findByUserId.mockResolvedValue([]);

			const result = await collectionService.getUserCollections('user-no-collections');

			expect(result).toEqual([]);
			expect(mockRepository.findByUserId).toHaveBeenCalledWith('user-no-collections');
		});
	});

	describe('getCollectionById', () => {
		it('should return collection by id for authorized user', async () => {
			const mockCollection: Collection = {
				id: '1',
				name: 'Test Collection',
				target_language: Language.EN,
				source_language: Language.VI,
				user_id: 'user-1',
				word_ids: [],
				paragraph_ids: [],
				keyword_ids: [],
				enable_learn_word_notification: false,
				created_at: new Date(),
				updated_at: new Date(),
			};

			mockRepository.findByUserIdAndId.mockResolvedValue(mockCollection);

			const result = await collectionService.getCollectionById('user-1', '1');

			expect(result).toEqual(mockCollection);
			expect(mockRepository.findByUserIdAndId).toHaveBeenCalledWith('user-1', '1');
		});

		it('should return null for unauthorized access', async () => {
			mockRepository.findByUserIdAndId.mockResolvedValue(null);

			const result = await collectionService.getCollectionById('user-2', '1');

			expect(result).toBeNull();
			expect(mockRepository.findByUserIdAndId).toHaveBeenCalledWith('user-2', '1');
		});
	});

	describe('createCollection', () => {
		it('should create a new collection', async () => {
			const collectionData = {
				name: 'New Collection',
				target_language: Language.EN,
				source_language: Language.VI,
				user_id: 'user-1',
				word_ids: [],
				paragraph_ids: [],
				keyword_ids: [],
				enable_learn_word_notification: false,
			};

			const mockCreatedCollection: Collection = {
				id: 'new-collection-id',
				created_at: new Date(),
				updated_at: new Date(),
				...collectionData,
			};

			mockRepository.create.mockResolvedValue(mockCreatedCollection);

			const result = await collectionService.createCollection(
				'user-1',
				'New Collection',
				Language.EN,
				Language.VI
			);

			expect(result).toEqual(mockCreatedCollection);
			expect(mockRepository.create).toHaveBeenCalledWith(
				expect.objectContaining({
					name: 'New Collection',
					target_language: Language.EN,
					source_language: Language.VI,
					user_id: 'user-1',
				})
			);
		});

		it('should create collection with default values', async () => {
			const mockCreatedCollection: Collection = {
				id: 'new-collection-id',
				name: 'Default Collection',
				target_language: Language.EN,
				source_language: Language.VI,
				user_id: 'user-1',
				word_ids: [],
				paragraph_ids: [],
				keyword_ids: [],
				enable_learn_word_notification: false,
				created_at: new Date(),
				updated_at: new Date(),
			};

			mockRepository.create.mockResolvedValue(mockCreatedCollection);

			const result = await collectionService.createCollection(
				'user-1',
				'Default Collection',
				Language.EN,
				Language.VI
			);

			expect(result.word_ids).toEqual([]);
			expect(result.paragraph_ids).toEqual([]);
			expect(result.keyword_ids).toEqual([]);
			expect(result.enable_learn_word_notification).toBe(false);
		});
	});

	describe('updateCollection', () => {
		it('should update collection for authorized user', async () => {
			const mockUpdatedCollection: Collection = {
				id: '1',
				name: 'Updated Collection Name',
				target_language: Language.EN,
				source_language: Language.VI,
				user_id: 'user-1',
				word_ids: [],
				paragraph_ids: [],
				keyword_ids: [],
				enable_learn_word_notification: true,
				created_at: new Date(),
				updated_at: new Date(),
			};

			mockRepository.findOne.mockResolvedValue(mockUpdatedCollection);
			mockRepository.update.mockResolvedValue(mockUpdatedCollection);

			const result = await collectionService.updateCollection(
				'user-1',
				'1',
				'Updated Collection Name'
			);

			expect(result).toEqual(mockUpdatedCollection);
			expect(mockRepository.findOne).toHaveBeenCalled();
			expect(mockRepository.update).toHaveBeenCalled();
		});

		it('should return null for unauthorized update', async () => {
			mockRepository.findOne.mockResolvedValue(null);

			const result = await collectionService.updateCollection('user-2', '1', 'Hack');

			expect(result).toBeNull();
			expect(mockRepository.findOne).toHaveBeenCalled();
			expect(mockRepository.update).not.toHaveBeenCalled();
		});
	});

	describe('deleteCollection', () => {
		it('should delete collection for authorized user', async () => {
			const mockCollection: Collection = {
				id: '1',
				name: 'To Delete',
				target_language: Language.EN,
				source_language: Language.VI,
				user_id: 'user-1',
				word_ids: [],
				paragraph_ids: [],
				keyword_ids: [],
				enable_learn_word_notification: false,
				created_at: new Date(),
				updated_at: new Date(),
			};

			mockRepository.findByUserIdAndId.mockResolvedValue(mockCollection);
			mockRepository.delete.mockResolvedValue(true);

			const result = await collectionService.deleteCollection('user-1', '1');

			expect(result).toBe(true);
			expect(mockRepository.findByUserIdAndId).toHaveBeenCalledWith('user-1', '1');
			expect(mockRepository.delete).toHaveBeenCalledWith('1');
		});

		it('should return false for unauthorized delete', async () => {
			mockRepository.findByUserIdAndId.mockResolvedValue(null);

			const result = await collectionService.deleteCollection('user-2', '1');

			expect(result).toBe(false);
			expect(mockRepository.findByUserIdAndId).toHaveBeenCalledWith('user-2', '1');
			expect(mockRepository.delete).not.toHaveBeenCalled();
		});
	});

	describe('addWordsToCollection', () => {
		it('should add words to collection', async () => {
			const mockCollection: Collection = {
				id: '1',
				name: 'Test Collection',
				target_language: Language.EN,
				source_language: Language.VI,
				user_id: 'user-1',
				word_ids: ['word-1'],
				paragraph_ids: [],
				keyword_ids: [],
				enable_learn_word_notification: false,
				created_at: new Date(),
				updated_at: new Date(),
			};

			const updatedCollection: Collection = {
				...mockCollection,
				word_ids: ['word-1', 'word-2', 'word-3'],
			};

			mockRepository.findByUserIdAndId.mockResolvedValue(mockCollection);
			mockRepository.updateWordIds.mockResolvedValue(updatedCollection);

			const result = await collectionService.addWordsToCollection('user-1', '1', [
				'word-2',
				'word-3',
			]);

			expect(result).toEqual(updatedCollection);
			expect(mockRepository.updateWordIds).toHaveBeenCalledWith('1', [
				'word-1',
				'word-2',
				'word-3',
			]);
		});

		it('should not add duplicate words', async () => {
			const mockCollection: Collection = {
				id: '1',
				name: 'Test Collection',
				target_language: Language.EN,
				source_language: Language.VI,
				user_id: 'user-1',
				word_ids: ['word-1', 'word-2'],
				paragraph_ids: [],
				keyword_ids: [],
				enable_learn_word_notification: false,
				created_at: new Date(),
				updated_at: new Date(),
			};

			const updatedCollection: Collection = {
				...mockCollection,
				word_ids: ['word-1', 'word-2', 'word-3'],
			};

			mockRepository.findByUserIdAndId.mockResolvedValue(mockCollection);
			mockRepository.updateWordIds.mockResolvedValue(updatedCollection);

			const result = await collectionService.addWordsToCollection('user-1', '1', [
				'word-2',
				'word-3',
			]);

			expect(result).toEqual(updatedCollection);
			expect(mockRepository.updateWordIds).toHaveBeenCalledWith('1', [
				'word-1',
				'word-2',
				'word-3',
			]);
		});
	});
});
