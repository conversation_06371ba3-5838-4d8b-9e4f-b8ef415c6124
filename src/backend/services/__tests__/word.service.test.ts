import { describe, it, expect, beforeEach, vi } from 'vitest';
import { WordServiceImpl } from '../word.service';
import { mockWordRepository } from '@/test/mocks/repositories';
import { Language, PartsOfSpeech } from '@prisma/client';
import { WordDetail } from '@/models/word';

describe('WordService', () => {
	let wordService: WordServiceImpl;
	let mockRepository: ReturnType<typeof mockWordRepository>;
	let mockGetWordRepository: () => any;
	let mockGetCollectionService: () => any;
	let mockGetLastSeenWordService: () => any;

	beforeEach(() => {
		mockRepository = mockWordRepository();
		mockGetWordRepository = vi.fn(() => mockRepository);
		mockGetCollectionService = vi.fn(() => ({}));
		mockGetLastSeenWordService = vi.fn(() => ({}));

		wordService = new WordServiceImpl(
			mockGetWordRepository,
			mockGetCollectionService,
			mockGetLastSeenWordService
		);
	});

	describe('searchWords', () => {
		it('should return words matching search term', async () => {
			const mockWords: WordDetail[] = [
				{
					id: '1',
					term: 'hello',
					language: Language.EN,
					audio_url: null,
					created_at: new Date(),
					updated_at: new Date(),
					definitions: [
						{
							id: 'def-1',
							word_id: '1',
							pos: [PartsOfSpeech.NOUN],
							ipa: '/həˈloʊ/',
							images: [],
							explains: [
								{
									id: 'exp-1',
									EN: 'A greeting',
									VI: 'Lời chào',
									definition_id: 'def-1',
								},
							],
							examples: [
								{
									id: 'ex-1',
									EN: 'Hello, how are you?',
									VI: 'Xin chào, bạn khỏe không?',
									definition_id: 'def-1',
								},
							],
						},
					],
				},
				{
					id: '2',
					term: 'help',
					language: Language.EN,
					audio_url: null,
					created_at: new Date(),
					updated_at: new Date(),
					definitions: [
						{
							id: 'def-2',
							word_id: '2',
							pos: [PartsOfSpeech.VERB],
							ipa: '/help/',
							images: [],
							explains: [
								{
									id: 'exp-2',
									EN: 'To assist',
									VI: 'Giúp đỡ',
									definition_id: 'def-2',
								},
							],
							examples: [
								{
									id: 'ex-2',
									EN: 'Can you help me?',
									VI: 'Bạn có thể giúp tôi không?',
									definition_id: 'def-2',
								},
							],
						},
					],
				},
			];

			mockRepository.find.mockResolvedValue(mockWords);

			const result = await wordService.searchWords('hel', Language.EN, 10);

			expect(result).toHaveLength(2);
			expect(result[0].term).toBe('hello');
			expect(result[1].term).toBe('help');
			expect(mockRepository.find).toHaveBeenCalled();
		});

		it('should handle empty search term', async () => {
			mockRepository.find.mockResolvedValue([]);

			const result = await wordService.searchWords('', Language.EN, 10);

			expect(result).toEqual([]);
			expect(mockRepository.find).toHaveBeenCalledWith({ limit: 10 });
		});

		it('should handle search with no results', async () => {
			mockRepository.find.mockResolvedValue([]);

			const result = await wordService.searchWords('xyz', Language.EN, 10);

			expect(result).toEqual([]);
			expect(mockRepository.find).toHaveBeenCalled();
		});
	});

	describe('getWordById', () => {
		it('should return word by id', async () => {
			const mockWord: WordDetail = {
				id: '1',
				term: 'test',
				language: Language.EN,
				audio_url: null,
				created_at: new Date(),
				updated_at: new Date(),
				definitions: [],
			};

			mockRepository.findById.mockResolvedValue(mockWord);

			const result = await wordService.getWordById('1');

			expect(result).toEqual(mockWord);
			expect(mockRepository.findById).toHaveBeenCalledWith('1');
		});

		it('should return null for non-existent word', async () => {
			mockRepository.findById.mockResolvedValue(null);

			const result = await wordService.getWordById('non-existent');

			expect(result).toBeNull();
			expect(mockRepository.findById).toHaveBeenCalledWith('non-existent');
		});
	});

	describe('findWordsByIds', () => {
		it('should return words by ids', async () => {
			const mockWords: WordDetail[] = [
				{
					id: '1',
					term: 'word1',
					language: Language.EN,
					audio_url: null,
					created_at: new Date(),
					updated_at: new Date(),
					definitions: [],
				},
				{
					id: '2',
					term: 'word2',
					language: Language.EN,
					audio_url: null,
					created_at: new Date(),
					updated_at: new Date(),
					definitions: [],
				},
			];

			mockRepository.findWordsByIds.mockResolvedValue(mockWords);

			const result = await wordService.findWordsByIds(['1', '2']);

			expect(result).toEqual(mockWords);
			expect(mockRepository.findWordsByIds).toHaveBeenCalledWith(['1', '2']);
		});

		it('should handle empty ids array', async () => {
			mockRepository.findWordsByIds.mockResolvedValue([]);

			const result = await wordService.findWordsByIds([]);

			expect(result).toEqual([]);
			expect(mockRepository.findWordsByIds).toHaveBeenCalledWith([]);
		});
	});

	describe('getWordsByTerms', () => {
		it('should return words by terms', async () => {
			const mockWords: WordDetail[] = [
				{
					id: '1',
					term: 'hello',
					language: Language.EN,
					audio_url: null,
					created_at: new Date(),
					updated_at: new Date(),
					definitions: [],
				},
			];

			mockRepository.find.mockResolvedValue(mockWords);

			const result = await wordService.getWordsByTerms(['hello'], Language.EN);

			expect(result).toEqual(mockWords);
			expect(mockRepository.find).toHaveBeenCalled();
		});
	});
});
