import { Language, Difficulty } from '@prisma/client';
import { PromptOptimizerService } from './prompt-optimizer.service';

export interface ModelConfig {
	name: string;
	maxTokens: number;
	costPer1kInput: number;
	costPer1kOutput: number;
	capabilities: ModelCapability[];
	performance: ModelPerformance;
	availability: 'high' | 'medium' | 'low';
}

export interface ModelCapability {
	type: 'text-generation' | 'translation' | 'evaluation' | 'creative' | 'analytical';
	quality: number; // 0-1 scale
	speed: number; // 0-1 scale
}

export interface ModelPerformance {
	averageLatency: number; // milliseconds
	reliability: number; // 0-1 scale
	qualityScore: number; // 0-1 scale
}

export interface RequestComplexity {
	tokenCount: number;
	operationType: string;
	requiresCreativity: boolean;
	requiresAccuracy: boolean;
	requiresSpeed: boolean;
	languagePair?: [Language, Language];
	difficulty?: Difficulty;
}

export interface ModelSelection {
	model: string;
	confidence: number;
	reasoning: string;
	estimatedCost: number;
	estimatedLatency: number;
	alternatives: Array<{
		model: string;
		score: number;
		reason: string;
	}>;
}

export class ModelSelectorService {
	private readonly models: Record<string, ModelConfig> = {
		'gpt-4o-mini': {
			name: 'gpt-4o-mini',
			maxTokens: 128000,
			costPer1kInput: 0.00015,
			costPer1kOutput: 0.0006,
			capabilities: [
				{ type: 'text-generation', quality: 0.85, speed: 0.9 },
				{ type: 'translation', quality: 0.8, speed: 0.9 },
				{ type: 'evaluation', quality: 0.75, speed: 0.9 },
				{ type: 'analytical', quality: 0.8, speed: 0.9 }
			],
			performance: {
				averageLatency: 1500,
				reliability: 0.95,
				qualityScore: 0.8
			},
			availability: 'high'
		},
		'gpt-4o': {
			name: 'gpt-4o',
			maxTokens: 128000,
			costPer1kInput: 0.005,
			costPer1kOutput: 0.015,
			capabilities: [
				{ type: 'text-generation', quality: 0.95, speed: 0.7 },
				{ type: 'translation', quality: 0.95, speed: 0.7 },
				{ type: 'evaluation', quality: 0.9, speed: 0.7 },
				{ type: 'creative', quality: 0.95, speed: 0.7 },
				{ type: 'analytical', quality: 0.95, speed: 0.7 }
			],
			performance: {
				averageLatency: 2500,
				reliability: 0.98,
				qualityScore: 0.95
			},
			availability: 'high'
		},
		'gpt-4': {
			name: 'gpt-4',
			maxTokens: 8192,
			costPer1kInput: 0.03,
			costPer1kOutput: 0.06,
			capabilities: [
				{ type: 'text-generation', quality: 0.9, speed: 0.5 },
				{ type: 'translation', quality: 0.9, speed: 0.5 },
				{ type: 'evaluation', quality: 0.95, speed: 0.5 },
				{ type: 'creative', quality: 0.9, speed: 0.5 },
				{ type: 'analytical', quality: 0.95, speed: 0.5 }
			],
			performance: {
				averageLatency: 4000,
				reliability: 0.95,
				qualityScore: 0.9
			},
			availability: 'medium'
		}
	};

	private readonly operationProfiles: Record<string, RequestComplexity> = {
		generateRandomTerms: {
			tokenCount: 800,
			operationType: 'text-generation',
			requiresCreativity: true,
			requiresAccuracy: false,
			requiresSpeed: true
		},
		generateWordDetails: {
			tokenCount: 1200,
			operationType: 'text-generation',
			requiresCreativity: false,
			requiresAccuracy: true,
			requiresSpeed: false
		},
		generateParagraph: {
			tokenCount: 1500,
			operationType: 'text-generation',
			requiresCreativity: true,
			requiresAccuracy: true,
			requiresSpeed: false
		},
		evaluateTranslation: {
			tokenCount: 1000,
			operationType: 'evaluation',
			requiresCreativity: false,
			requiresAccuracy: true,
			requiresSpeed: false
		},
		evaluateAnswers: {
			tokenCount: 1800,
			operationType: 'evaluation',
			requiresCreativity: false,
			requiresAccuracy: true,
			requiresSpeed: false
		},
		generateQuestions: {
			tokenCount: 1000,
			operationType: 'text-generation',
			requiresCreativity: true,
			requiresAccuracy: true,
			requiresSpeed: false
		},
		generateGrammarPractice: {
			tokenCount: 2000,
			operationType: 'creative',
			requiresCreativity: true,
			requiresAccuracy: true,
			requiresSpeed: false
		}
	};

	/**
	 * Select optimal model for request
	 */
	selectModel(
		operation: string,
		params: Record<string, any> = {},
		constraints: {
			maxCost?: number;
			maxLatency?: number;
			minQuality?: number;
		} = {}
	): ModelSelection {
		// Get request complexity
		const complexity = this.analyzeComplexity(operation, params);
		
		// Score all available models
		const modelScores = this.scoreModels(complexity, constraints);
		
		// Select best model
		const bestModel = modelScores[0];
		
		if (!bestModel) {
			throw new Error('No suitable model found for request');
		}

		const modelConfig = this.models[bestModel.model];
		
		return {
			model: bestModel.model,
			confidence: bestModel.score,
			reasoning: bestModel.reason,
			estimatedCost: this.estimateCost(modelConfig, complexity.tokenCount),
			estimatedLatency: modelConfig.performance.averageLatency,
			alternatives: modelScores.slice(1, 4).map(score => ({
				model: score.model,
				score: score.score,
				reason: score.reason
			}))
		};
	}

	/**
	 * Analyze request complexity
	 */
	private analyzeComplexity(operation: string, params: Record<string, any>): RequestComplexity {
		// Start with operation profile
		const baseComplexity = this.operationProfiles[operation] || {
			tokenCount: 1000,
			operationType: 'text-generation',
			requiresCreativity: false,
			requiresAccuracy: true,
			requiresSpeed: false
		};

		// Adjust based on parameters
		let adjustedTokenCount = baseComplexity.tokenCount;
		
		// Adjust for content size
		if (params.count && typeof params.count === 'number') {
			adjustedTokenCount *= Math.min(params.count, 10); // Cap at 10x
		}
		
		if (params.maxTerms && typeof params.maxTerms === 'number') {
			adjustedTokenCount *= Math.min(params.maxTerms / 10, 3); // Scale with terms
		}

		// Adjust for complexity indicators
		if (params.keywords && Array.isArray(params.keywords)) {
			adjustedTokenCount += params.keywords.length * 50;
		}

		if (params.excludes && Array.isArray(params.excludes)) {
			adjustedTokenCount += params.excludes.length * 30;
		}

		// Estimate actual token count if prompt available
		if (params.systemPrompt) {
			const estimatedTokens = PromptOptimizerService.estimateTokens(params.systemPrompt);
			adjustedTokenCount = Math.max(adjustedTokenCount, estimatedTokens);
		}

		return {
			...baseComplexity,
			tokenCount: adjustedTokenCount,
			languagePair: params.source_language && params.target_language 
				? [params.source_language, params.target_language]
				: undefined,
			difficulty: params.difficulty
		};
	}

	/**
	 * Score models based on complexity and constraints
	 */
	private scoreModels(
		complexity: RequestComplexity,
		constraints: {
			maxCost?: number;
			maxLatency?: number;
			minQuality?: number;
		}
	): Array<{ model: string; score: number; reason: string }> {
		const scores: Array<{ model: string; score: number; reason: string }> = [];

		for (const [modelName, config] of Object.entries(this.models)) {
			// Check hard constraints
			const estimatedCost = this.estimateCost(config, complexity.tokenCount);
			
			if (constraints.maxCost && estimatedCost > constraints.maxCost) {
				continue;
			}
			
			if (constraints.maxLatency && config.performance.averageLatency > constraints.maxLatency) {
				continue;
			}
			
			if (constraints.minQuality && config.performance.qualityScore < constraints.minQuality) {
				continue;
			}

			// Check token limits
			if (complexity.tokenCount > config.maxTokens * 0.8) { // Leave 20% buffer
				continue;
			}

			// Calculate score
			const score = this.calculateModelScore(config, complexity);
			const reason = this.generateReasoning(config, complexity, score);
			
			scores.push({ model: modelName, score, reason });
		}

		// Sort by score descending
		return scores.sort((a, b) => b.score - a.score);
	}

	/**
	 * Calculate model score for given complexity
	 */
	private calculateModelScore(config: ModelConfig, complexity: RequestComplexity): number {
		let score = 0;
		let totalWeight = 0;

		// Capability matching
		const relevantCapability = config.capabilities.find(cap => 
			cap.type === complexity.operationType
		);
		
		if (relevantCapability) {
			score += relevantCapability.quality * 0.4;
			totalWeight += 0.4;
			
			if (complexity.requiresSpeed) {
				score += relevantCapability.speed * 0.2;
				totalWeight += 0.2;
			}
		}

		// Performance factors
		score += config.performance.qualityScore * 0.3;
		totalWeight += 0.3;

		score += config.performance.reliability * 0.1;
		totalWeight += 0.1;

		// Cost efficiency (inverse cost factor)
		const estimatedCost = this.estimateCost(config, complexity.tokenCount);
		const costEfficiency = Math.max(0, 1 - (estimatedCost / 0.1)); // Normalize to $0.10 max
		score += costEfficiency * 0.15;
		totalWeight += 0.15;

		// Availability factor
		const availabilityScore = config.availability === 'high' ? 1 : 
								 config.availability === 'medium' ? 0.7 : 0.4;
		score += availabilityScore * 0.05;
		totalWeight += 0.05;

		// Normalize score
		return totalWeight > 0 ? score / totalWeight : 0;
	}

	/**
	 * Generate reasoning for model selection
	 */
	private generateReasoning(config: ModelConfig, complexity: RequestComplexity, score: number): string {
		const reasons: string[] = [];

		// Primary capability match
		const relevantCap = config.capabilities.find(cap => cap.type === complexity.operationType);
		if (relevantCap) {
			reasons.push(`Strong ${complexity.operationType} capability (${(relevantCap.quality * 100).toFixed(0)}%)`);
		}

		// Cost consideration
		const estimatedCost = this.estimateCost(config, complexity.tokenCount);
		if (estimatedCost < 0.01) {
			reasons.push('Cost-effective');
		} else if (estimatedCost > 0.05) {
			reasons.push('Higher cost but premium quality');
		}

		// Performance factors
		if (config.performance.reliability > 0.95) {
			reasons.push('High reliability');
		}

		if (complexity.requiresSpeed && config.performance.averageLatency < 2000) {
			reasons.push('Fast response time');
		}

		if (complexity.requiresAccuracy && config.performance.qualityScore > 0.9) {
			reasons.push('High accuracy');
		}

		return reasons.join(', ') || `Overall score: ${(score * 100).toFixed(0)}%`;
	}

	/**
	 * Estimate cost for model and token count
	 */
	private estimateCost(config: ModelConfig, tokenCount: number): number {
		// Assume 70% input, 30% output tokens
		const inputTokens = tokenCount * 0.7;
		const outputTokens = tokenCount * 0.3;
		
		const inputCost = (inputTokens / 1000) * config.costPer1kInput;
		const outputCost = (outputTokens / 1000) * config.costPer1kOutput;
		
		return inputCost + outputCost;
	}

	/**
	 * Get model configuration
	 */
	getModelConfig(modelName: string): ModelConfig | null {
		return this.models[modelName] || null;
	}

	/**
	 * Get all available models
	 */
	getAvailableModels(): string[] {
		return Object.keys(this.models);
	}

	/**
	 * Update model performance based on actual usage
	 */
	updateModelPerformance(
		modelName: string, 
		actualLatency: number, 
		qualityFeedback: number,
		success: boolean
	): void {
		const config = this.models[modelName];
		if (!config) return;

		// Update with exponential moving average
		const alpha = 0.1; // Learning rate
		
		config.performance.averageLatency = 
			config.performance.averageLatency * (1 - alpha) + actualLatency * alpha;
		
		if (qualityFeedback >= 0) {
			config.performance.qualityScore = 
				config.performance.qualityScore * (1 - alpha) + qualityFeedback * alpha;
		}
		
		// Update reliability based on success rate
		const reliabilityUpdate = success ? 1 : 0;
		config.performance.reliability = 
			config.performance.reliability * (1 - alpha) + reliabilityUpdate * alpha;
	}

	/**
	 * Get model selection statistics
	 */
	getSelectionStats(): Record<string, any> {
		const stats: Record<string, any> = {};
		
		for (const [modelName, config] of Object.entries(this.models)) {
			stats[modelName] = {
				performance: config.performance,
				capabilities: config.capabilities.length,
				availability: config.availability,
				costPer1kTokens: (config.costPer1kInput + config.costPer1kOutput) / 2
			};
		}
		
		return stats;
	}
}

// Singleton instance
export const modelSelector = new ModelSelectorService();
