export interface TokenUsageRecord {
	id: string;
	timestamp: Date;
	endpoint: string;
	operation: string;
	inputTokens: number;
	outputTokens: number;
	totalTokens: number;
	estimatedCost: number;
	model: string;
	userId?: string;
	optimized: boolean;
	compressionRatio?: number;
}

export interface TokenUsageStats {
	totalTokens: number;
	totalCost: number;
	requestCount: number;
	averageTokensPerRequest: number;
	averageCostPerRequest: number;
	optimizationSavings: {
		tokensSaved: number;
		costSaved: number;
		compressionRatio: number;
	};
}

export interface CostAnalysis {
	daily: TokenUsageStats;
	weekly: TokenUsageStats;
	monthly: TokenUsageStats;
	byOperation: Record<string, TokenUsageStats>;
	trends: {
		tokenUsageTrend: number; // Percentage change
		costTrend: number;
		optimizationTrend: number;
	};
}

export interface OptimizationSuggestion {
	type: 'cache' | 'prompt' | 'batch' | 'model';
	operation: string;
	description: string;
	estimatedSavings: {
		tokens: number;
		cost: number;
		percentage: number;
	};
	priority: 'high' | 'medium' | 'low';
	implementation: string;
}

export class TokenMonitorService {
	private usageRecords: TokenUsageRecord[] = [];
	private readonly MODEL_COSTS = {
		'gpt-4o-mini': {
			input: 0.00015,  // per 1K tokens
			output: 0.0006   // per 1K tokens
		},
		'gpt-4o': {
			input: 0.005,
			output: 0.015
		},
		'gpt-4': {
			input: 0.03,
			output: 0.06
		}
	};

	/**
	 * Track a token usage event
	 */
	trackUsage(record: Omit<TokenUsageRecord, 'id' | 'timestamp' | 'totalTokens' | 'estimatedCost'>): void {
		const totalTokens = record.inputTokens + record.outputTokens;
		const estimatedCost = this.calculateCost(record.model, record.inputTokens, record.outputTokens);

		const fullRecord: TokenUsageRecord = {
			...record,
			id: this.generateId(),
			timestamp: new Date(),
			totalTokens,
			estimatedCost
		};

		this.usageRecords.push(fullRecord);

		// Keep only last 10000 records to prevent memory issues
		if (this.usageRecords.length > 10000) {
			this.usageRecords = this.usageRecords.slice(-10000);
		}

		// Log if monitoring is enabled
		if (process.env.LLM_MONITORING_ENABLED === 'true') {
			console.log(`[TokenMonitor] ${record.operation}: ${totalTokens} tokens, $${estimatedCost.toFixed(4)}`);
		}
	}

	/**
	 * Calculate cost for token usage
	 */
	private calculateCost(model: string, inputTokens: number, outputTokens: number): number {
		const costs = this.MODEL_COSTS[model] || this.MODEL_COSTS['gpt-4o-mini'];
		
		const inputCost = (inputTokens / 1000) * costs.input;
		const outputCost = (outputTokens / 1000) * costs.output;
		
		return inputCost + outputCost;
	}

	/**
	 * Get usage statistics for a time period
	 */
	getStats(timeframe: 'day' | 'week' | 'month' = 'day'): TokenUsageStats {
		const now = new Date();
		const cutoff = new Date();
		
		switch (timeframe) {
			case 'day':
				cutoff.setDate(now.getDate() - 1);
				break;
			case 'week':
				cutoff.setDate(now.getDate() - 7);
				break;
			case 'month':
				cutoff.setMonth(now.getMonth() - 1);
				break;
		}

		const relevantRecords = this.usageRecords.filter(r => r.timestamp >= cutoff);
		
		return this.calculateStats(relevantRecords);
	}

	/**
	 * Calculate statistics from records
	 */
	private calculateStats(records: TokenUsageRecord[]): TokenUsageStats {
		if (records.length === 0) {
			return {
				totalTokens: 0,
				totalCost: 0,
				requestCount: 0,
				averageTokensPerRequest: 0,
				averageCostPerRequest: 0,
				optimizationSavings: {
					tokensSaved: 0,
					costSaved: 0,
					compressionRatio: 1
				}
			};
		}

		const totalTokens = records.reduce((sum, r) => sum + r.totalTokens, 0);
		const totalCost = records.reduce((sum, r) => sum + r.estimatedCost, 0);
		const requestCount = records.length;

		// Calculate optimization savings
		const optimizedRecords = records.filter(r => r.optimized);
		const avgCompressionRatio = optimizedRecords.length > 0 
			? optimizedRecords.reduce((sum, r) => sum + (r.compressionRatio || 1), 0) / optimizedRecords.length
			: 1;

		const estimatedOriginalTokens = totalTokens / avgCompressionRatio;
		const tokensSaved = estimatedOriginalTokens - totalTokens;
		const costSaved = tokensSaved * 0.0003; // Rough average cost per token

		return {
			totalTokens,
			totalCost,
			requestCount,
			averageTokensPerRequest: totalTokens / requestCount,
			averageCostPerRequest: totalCost / requestCount,
			optimizationSavings: {
				tokensSaved,
				costSaved,
				compressionRatio: avgCompressionRatio
			}
		};
	}

	/**
	 * Get comprehensive cost analysis
	 */
	getCostAnalysis(): CostAnalysis {
		const daily = this.getStats('day');
		const weekly = this.getStats('week');
		const monthly = this.getStats('month');

		// Group by operation
		const byOperation: Record<string, TokenUsageStats> = {};
		const operations = [...new Set(this.usageRecords.map(r => r.operation))];
		
		for (const operation of operations) {
			const operationRecords = this.usageRecords.filter(r => r.operation === operation);
			byOperation[operation] = this.calculateStats(operationRecords);
		}

		// Calculate trends (simplified - compare last 7 days vs previous 7 days)
		const last7Days = this.getRecordsInRange(7);
		const previous7Days = this.getRecordsInRange(14, 7);
		
		const trends = this.calculateTrends(last7Days, previous7Days);

		return {
			daily,
			weekly,
			monthly,
			byOperation,
			trends
		};
	}

	/**
	 * Get optimization suggestions
	 */
	getOptimizationSuggestions(): OptimizationSuggestion[] {
		const suggestions: OptimizationSuggestion[] = [];
		const analysis = this.getCostAnalysis();

		// Suggest caching for high-frequency operations
		for (const [operation, stats] of Object.entries(analysis.byOperation)) {
			if (stats.requestCount > 10 && stats.averageCostPerRequest > 0.001) {
				suggestions.push({
					type: 'cache',
					operation,
					description: `High frequency operation with ${stats.requestCount} requests. Implement caching to reduce API calls.`,
					estimatedSavings: {
						tokens: Math.floor(stats.totalTokens * 0.7),
						cost: stats.totalCost * 0.7,
						percentage: 70
					},
					priority: stats.totalCost > 1 ? 'high' : 'medium',
					implementation: `Add caching with TTL based on content stability for ${operation}`
				});
			}
		}

		// Suggest prompt optimization for token-heavy operations
		const heavyOperations = Object.entries(analysis.byOperation)
			.filter(([_, stats]) => stats.averageTokensPerRequest > 1000)
			.sort((a, b) => b[1].totalTokens - a[1].totalTokens);

		for (const [operation, stats] of heavyOperations.slice(0, 3)) {
			suggestions.push({
				type: 'prompt',
				operation,
				description: `High token usage: ${stats.averageTokensPerRequest} tokens/request. Optimize prompts to reduce token consumption.`,
				estimatedSavings: {
					tokens: Math.floor(stats.totalTokens * 0.4),
					cost: stats.totalCost * 0.4,
					percentage: 40
				},
				priority: 'high',
				implementation: `Use PromptOptimizerService to compress prompts for ${operation}`
			});
		}

		return suggestions.sort((a, b) => {
			const priorityOrder = { high: 3, medium: 2, low: 1 };
			return priorityOrder[b.priority] - priorityOrder[a.priority];
		});
	}

	/**
	 * Check if budget thresholds are exceeded
	 */
	checkBudgetAlerts(): { type: string; message: string; severity: 'warning' | 'critical' }[] {
		const alerts = [];
		const dailyStats = this.getStats('day');
		const monthlyStats = this.getStats('month');

		// Daily budget check
		const dailyBudget = Number.parseInt(process.env.LLM_TOKEN_BUDGET_DAILY || '100000');
		if (dailyStats.totalTokens > dailyBudget * 0.8) {
			alerts.push({
				type: 'daily_budget',
				message: `Daily token usage (${dailyStats.totalTokens}) approaching limit (${dailyBudget})`,
				severity: dailyStats.totalTokens > dailyBudget ? 'critical' : 'warning'
			});
		}

		// Cost alerts
		const dailyCostThreshold = Number.parseFloat(process.env.LLM_COST_ALERT_DAILY || '10.0');
		if (dailyStats.totalCost > dailyCostThreshold * 0.8) {
			alerts.push({
				type: 'daily_cost',
				message: `Daily cost ($${dailyStats.totalCost.toFixed(2)}) approaching threshold ($${dailyCostThreshold})`,
				severity: dailyStats.totalCost > dailyCostThreshold ? 'critical' : 'warning'
			});
		}

		return alerts;
	}

	/**
	 * Helper methods
	 */
	private generateId(): string {
		return Date.now().toString(36) + Math.random().toString(36).substr(2);
	}

	private getRecordsInRange(days: number, offset: number = 0): TokenUsageRecord[] {
		const now = new Date();
		const end = new Date(now.getTime() - offset * 24 * 60 * 60 * 1000);
		const start = new Date(end.getTime() - days * 24 * 60 * 60 * 1000);
		
		return this.usageRecords.filter(r => r.timestamp >= start && r.timestamp <= end);
	}

	private calculateTrends(current: TokenUsageRecord[], previous: TokenUsageRecord[]): any {
		const currentStats = this.calculateStats(current);
		const previousStats = this.calculateStats(previous);

		const tokenTrend = previousStats.totalTokens > 0 
			? ((currentStats.totalTokens - previousStats.totalTokens) / previousStats.totalTokens) * 100
			: 0;

		const costTrend = previousStats.totalCost > 0
			? ((currentStats.totalCost - previousStats.totalCost) / previousStats.totalCost) * 100
			: 0;

		const optimizationTrend = previousStats.optimizationSavings.compressionRatio > 0
			? ((currentStats.optimizationSavings.compressionRatio - previousStats.optimizationSavings.compressionRatio) / previousStats.optimizationSavings.compressionRatio) * 100
			: 0;

		return {
			tokenUsageTrend: tokenTrend,
			costTrend: costTrend,
			optimizationTrend: optimizationTrend
		};
	}
}

// Singleton instance
export const tokenMonitor = new TokenMonitorService();
