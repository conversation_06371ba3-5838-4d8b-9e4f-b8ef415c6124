'use server';

import { ValidationError } from '@/backend/errors';
import { getFeedbackService } from '@/backend/wire';
import { auth } from '@/lib';
import { Feedback } from '@prisma/client';

/**
 * Submits user feedback to the system.
 * @param message - The feedback message content.
 * @returns The created feedback object.
 * @throws {ValidationError} If message is missing.
 * @throws {Error} If submission fails for other reasons.
 */
export async function submitFeedbackApi(message: string): Promise<Feedback> {
	// Validate required fields
	if (!message) {
		throw new ValidationError('Message is required');
	}

	// Ensure user is authenticated and get the user ID
	const session = await auth();
	if (!session?.user?.id) {
		// If authentication fails or user ID is missing, throw an error as it's required.
		// Using a generic Error is appropriate as it's not a user input validation issue.
		throw new Error('Authentication required to submit feedback');
	}
	// authUserId is now guaranteed to be a string
	const authUserId = session.user.id;

	const feedbackService = getFeedbackService();
	try {
		// Assuming FeedbackService.createFeedback has a signature compatible with
		// (feedbackMessage: string, authenticatedUserId: string | undefined)
		// Note: The diagnostic error suggests the second argument must be `string`,
		// implying the service might require an authenticated user ID.
		// This rewrite removes the email parameter as requested, but preserves
		// the original attempt to pass authUserId, leaving the potential type
		// error if the service strictly requires a string user ID.
		const feedback = await feedbackService.createFeedback(message, authUserId);
		return feedback;
	} catch (error) {
		if (error instanceof ValidationError) {
			// Catch and rethrow if service throws ValidationError
			throw error;
		}
		// Log the detailed error for server-side debugging
		console.error('Failed to submit feedback:', error);
		// Throw a generic error for the client
		throw new Error('Failed to submit feedback');
	}
}
