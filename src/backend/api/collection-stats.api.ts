import { getCollectionStatsService } from '@/backend/wire';
import { DailyStats } from '@/backend/services/collection-stats.service';

export async function getCollectionStatsApi(
	collectionId: string,
	userId: string,
	days?: number
): Promise<DailyStats[]> {
	const collectionStatsService = getCollectionStatsService();
	return collectionStatsService.getStatsForCollection(collectionId, userId, days);
}

export async function getTodayStatsApi(
	collectionId: string,
	userId: string
): Promise<DailyStats> {
	const collectionStatsService = getCollectionStatsService();
	return collectionStatsService.getTodayStats(collectionId, userId);
}

export async function trackWordReviewApi(
	collectionId: string,
	userId: string,
	count?: number
): Promise<void> {
	const collectionStatsService = getCollectionStatsService();
	return collectionStatsService.trackWordReview(collectionId, userId, count);
}

export async function trackQAPracticeSubmissionApi(
	collectionId: string,
	userId: string,
	count?: number
): Promise<void> {
	const collectionStatsService = getCollectionStatsService();
	return collectionStatsService.trackQAPracticeSubmission(collectionId, userId, count);
}

export async function trackParagraphPracticeSubmissionApi(
	collectionId: string,
	userId: string,
	count?: number
): Promise<void> {
	const collectionStatsService = getCollectionStatsService();
	return collectionStatsService.trackParagraphPracticeSubmission(collectionId, userId, count);
}
