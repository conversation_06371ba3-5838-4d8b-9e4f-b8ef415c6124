'use client';

import { ErrorSeverity } from '@/lib/error-handling';
import { useError } from '@/contexts/error-context';
import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, AlertTriangle, CheckCircle, Info, RefreshCw, X, XCircle } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface ToastError {
	id: string;
	message: string;
	severity: ErrorSeverity;
	category: string;
	timestamp: string;
	autoHide?: boolean;
	duration?: number;
}

interface ErrorToastProps {
	error: ToastError;
	onDismiss: (errorId: string) => void;
	onRetry?: (error: ToastError) => void;
}

interface ErrorToastContainerProps {
	maxToasts?: number;
	position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

// ============================================================================
// INDIVIDUAL ERROR TOAST
// ============================================================================

function ErrorToast({ error, onDismiss, onRetry }: ErrorToastProps) {
	const [isRetrying, setIsRetrying] = useState(false);
	const [timeLeft, setTimeLeft] = useState(error.duration || 5000);

	const message = error.message;
	const canRetry =
		onRetry &&
		(error.severity === ErrorSeverity.HIGH || error.severity === ErrorSeverity.MEDIUM);

	// Auto-hide timer
	useEffect(() => {
		if (!error.autoHide) return;

		const interval = setInterval(() => {
			setTimeLeft((prev) => {
				if (prev <= 100) {
					onDismiss(error.id);
					return 0;
				}
				return prev - 100;
			});
		}, 100);

		return () => clearInterval(interval);
	}, [error.autoHide, error.id, onDismiss]);

	const handleRetry = async () => {
		if (!onRetry || isRetrying) return;

		setIsRetrying(true);
		try {
			onRetry(error);
			onDismiss(error.id);
		} catch {
			// Retry failed, keep the toast visible
		} finally {
			setIsRetrying(false);
		}
	};

	const getToastIcon = () => {
		switch (error.severity) {
			case ErrorSeverity.CRITICAL:
				return <XCircle className="h-5 w-5 text-red-500" />;
			case ErrorSeverity.HIGH:
				return <AlertCircle className="h-5 w-5 text-red-500" />;
			case ErrorSeverity.MEDIUM:
				return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
			case ErrorSeverity.LOW:
				return <Info className="h-5 w-5 text-blue-500" />;
			default:
				return <AlertCircle className="h-5 w-5 text-gray-500" />;
		}
	};

	const getToastStyles = () => {
		switch (error.severity) {
			case ErrorSeverity.CRITICAL:
				return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
			case ErrorSeverity.HIGH:
				return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
			case ErrorSeverity.MEDIUM:
				return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
			case ErrorSeverity.LOW:
				return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800';
			default:
				return 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800';
		}
	};

	const progressPercentage =
		error.autoHide && error.duration ? ((error.duration - timeLeft) / error.duration) * 100 : 0;

	return (
		<motion.div
			initial={{ opacity: 0, x: 300, scale: 0.3 }}
			animate={{ opacity: 1, x: 0, scale: 1 }}
			exit={{ opacity: 0, x: 300, scale: 0.5, transition: { duration: 0.2 } }}
			className={`
				relative max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto 
				border ${getToastStyles()} overflow-hidden
			`}
		>
			{/* Progress bar for auto-hide */}
			{error.autoHide && (
				<div className="absolute top-0 left-0 h-1 bg-gray-200 dark:bg-gray-700 w-full">
					<div
						className="h-full bg-blue-500 transition-all duration-100 ease-linear"
						style={{ width: `${progressPercentage}%` }}
					/>
				</div>
			)}

			<div className="p-4">
				<div className="flex items-start">
					<div className="flex-shrink-0">{getToastIcon()}</div>
					<div className="ml-3 w-0 flex-1">
						<p className="text-sm font-medium text-gray-900 dark:text-white">
							{message}
						</p>
						{error.id && (
							<p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
								ID: {error.id.slice(-8)}
							</p>
						)}

						{canRetry && (
							<div className="mt-2">
								<button
									onClick={handleRetry}
									disabled={isRetrying}
									className="inline-flex items-center text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 disabled:opacity-50"
								>
									<RefreshCw
										className={`h-3 w-3 mr-1 ${
											isRetrying ? 'animate-spin' : ''
										}`}
									/>
									{isRetrying ? 'Retrying...' : 'Retry'}
								</button>
							</div>
						)}
					</div>
					<div className="ml-4 flex-shrink-0 flex">
						<button
							onClick={() => onDismiss(error.id)}
							className="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
						>
							<span className="sr-only">Close</span>
							<X className="h-5 w-5" />
						</button>
					</div>
				</div>
			</div>
		</motion.div>
	);
}

// ============================================================================
// ERROR TOAST CONTAINER
// ============================================================================

export function ErrorToastContainer({
	maxToasts = 5,
	position = 'top-right',
}: ErrorToastContainerProps) {
	const { errorState, removeError } = useError();
	const [toastErrors, setToastErrors] = useState<ToastError[]>([]);

	// Convert errors to toast errors with auto-hide settings
	useEffect(() => {
		const newToastErrors: ToastError[] = errorState.errors.slice(-maxToasts).map((error) => ({
			id: error.id,
			message: error.message,
			severity: error.severity,
			category: error.category,
			timestamp: error.timestamp,
			autoHide:
				error.severity === ErrorSeverity.LOW || error.severity === ErrorSeverity.MEDIUM,
			duration: error.severity === ErrorSeverity.LOW ? 3000 : 5000,
		}));

		setToastErrors(newToastErrors);
	}, [errorState.errors, maxToasts]);

	const handleDismiss = useCallback(
		(errorId: string) => {
			removeError(errorId);
		},
		[removeError]
	);

	const handleRetry = useCallback(
		(error: ToastError) => {
			// This would typically trigger a retry of the failed operation
			// For now, we'll just remove the error
			removeError(error.id);
		},
		[removeError]
	);

	const getPositionStyles = () => {
		switch (position) {
			case 'top-right':
				return 'top-0 right-0';
			case 'top-left':
				return 'top-0 left-0';
			case 'bottom-right':
				return 'bottom-0 right-0';
			case 'bottom-left':
				return 'bottom-0 left-0';
			default:
				return 'top-0 right-0';
		}
	};

	if (toastErrors.length === 0) return null;

	return (
		<div
			className={`fixed ${getPositionStyles()} z-50 p-4 space-y-4 pointer-events-none`}
			style={{ maxWidth: '420px' }}
		>
			<AnimatePresence>
				{toastErrors.map((error) => (
					<ErrorToast
						key={error.id}
						error={error}
						onDismiss={handleDismiss}
						onRetry={handleRetry}
					/>
				))}
			</AnimatePresence>
		</div>
	);
}

// ============================================================================
// SUCCESS TOAST (for positive feedback)
// ============================================================================

interface SuccessToastProps {
	message: string;
	onDismiss: () => void;
	autoHide?: boolean;
	duration?: number;
}

export function SuccessToast({
	message,
	onDismiss,
	autoHide = true,
	duration = 3000,
}: SuccessToastProps) {
	const [timeLeft, setTimeLeft] = useState(duration);

	useEffect(() => {
		if (!autoHide) return;

		const interval = setInterval(() => {
			setTimeLeft((prev) => {
				if (prev <= 100) {
					onDismiss();
					return 0;
				}
				return prev - 100;
			});
		}, 100);

		return () => clearInterval(interval);
	}, [autoHide, onDismiss]);

	const progressPercentage = autoHide ? ((duration - timeLeft) / duration) * 100 : 0;

	return (
		<motion.div
			initial={{ opacity: 0, x: 300, scale: 0.3 }}
			animate={{ opacity: 1, x: 0, scale: 1 }}
			exit={{ opacity: 0, x: 300, scale: 0.5, transition: { duration: 0.2 } }}
			className="relative max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto border border-green-200 dark:border-green-800 overflow-hidden"
		>
			{autoHide && (
				<div className="absolute top-0 left-0 h-1 bg-gray-200 dark:bg-gray-700 w-full">
					<div
						className="h-full bg-green-500 transition-all duration-100 ease-linear"
						style={{ width: `${progressPercentage}%` }}
					/>
				</div>
			)}

			<div className="p-4">
				<div className="flex items-start">
					<div className="flex-shrink-0">
						<CheckCircle className="h-5 w-5 text-green-500" />
					</div>
					<div className="ml-3 w-0 flex-1">
						<p className="text-sm font-medium text-gray-900 dark:text-white">
							{message}
						</p>
					</div>
					<div className="ml-4 flex-shrink-0 flex">
						<button
							onClick={onDismiss}
							className="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
						>
							<span className="sr-only">Close</span>
							<X className="h-5 w-5" />
						</button>
					</div>
				</div>
			</div>
		</motion.div>
	);
}
