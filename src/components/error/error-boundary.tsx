'use client';

import { AppError, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorSeverity, errorLogger } from '@/lib/error-handling';
import { Component, ErrorInfo, ReactNode } from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface ErrorBoundaryState {
	hasError: boolean;
	error: AppError | null;
	errorInfo: ErrorInfo | null;
}

interface ErrorBoundaryProps {
	children: ReactNode;
	fallback?: (error: AppError, errorInfo: ErrorInfo | null, retry: () => void) => ReactNode;
	onError?: (error: AppError, errorInfo: ErrorInfo) => void;
	level?: 'page' | 'section' | 'component';
	name?: string;
}

// ============================================================================
// ERROR BOUNDARY COMPONENT
// ============================================================================

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
	private retryTimeoutId: NodeJS.Timeout | null = null;

	constructor(props: ErrorBoundaryProps) {
		super(props);
		this.state = {
			hasError: false,
			error: null,
			errorInfo: null,
		};
	}

	static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
		// Convert the error to AppError
		const appError = new AppError(
			error.message || 'An unexpected error occurred',
			'REACT_ERROR_BOUNDARY',
			500,
			ErrorSeverity.HIGH,
			ErrorCategory.CLIENT,
			{
				application: {
					component: 'ErrorBoundary',
					action: 'component_error',
				},
				additionalData: {
					originalError: error.name,
					stack: error.stack,
				},
			}
		);

		return {
			hasError: true,
			error: appError,
		};
	}

	componentDidCatch(error: Error, errorInfo: ErrorInfo) {
		const { onError, name } = this.props;

		// Create enhanced error with React error info
		const appError = new AppError(
			error.message || 'React component error',
			'REACT_COMPONENT_ERROR',
			500,
			ErrorSeverity.HIGH,
			ErrorCategory.CLIENT,
			{
				application: {
					component: name || 'ErrorBoundary',
					action: 'component_did_catch',
					state: {
						componentStack: errorInfo.componentStack,
					},
				},
				additionalData: {
					errorInfo,
					originalError: {
						name: error.name,
						message: error.message,
						stack: error.stack,
					},
				},
			}
		);

		// Log the error
		errorLogger.error(
			`React Error Boundary caught error in ${name || 'unknown component'}`,
			appError,
			{ errorInfo },
			'ErrorBoundary'
		);

		// Update state with error info
		this.setState({
			error: appError,
			errorInfo,
		});

		// Call custom error handler if provided
		if (onError) {
			onError(appError, errorInfo);
		}
	}

	componentWillUnmount() {
		if (this.retryTimeoutId) {
			clearTimeout(this.retryTimeoutId);
		}
	}

	retry = () => {
		// Clear the error state to retry rendering
		this.setState({
			hasError: false,
			error: null,
			errorInfo: null,
		});

		// Log the retry attempt
		errorLogger.info(
			`Error boundary retry attempted for ${this.props.name || 'unknown component'}`,
			{},
			'ErrorBoundary'
		);
	};

	render() {
		const { hasError, error, errorInfo } = this.state;
		const { children, fallback, level = 'component' } = this.props;

		if (hasError && error) {
			// Use custom fallback if provided
			if (fallback) {
				return fallback(error, errorInfo, this.retry);
			}

			// Default fallback based on level
			return this.renderDefaultFallback(error, errorInfo, level);
		}

		return children;
	}

	private renderDefaultFallback(error: AppError, errorInfo: ErrorInfo | null, level: string) {
		switch (level) {
			case 'page':
				return <PageErrorFallback error={error} retry={this.retry} />;
			case 'section':
				return <SectionErrorFallback error={error} retry={this.retry} />;
			default:
				return <ComponentErrorFallback error={error} retry={this.retry} />;
		}
	}
}

// ============================================================================
// DEFAULT FALLBACK COMPONENTS
// ============================================================================

interface ErrorFallbackProps {
	error: AppError;
	retry: () => void;
}

function PageErrorFallback({ error, retry }: ErrorFallbackProps) {
	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
			<div className="max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
				<div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full mb-4">
					<svg
						className="w-6 h-6 text-red-600 dark:text-red-400"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
						/>
					</svg>
				</div>
				<h1 className="text-xl font-semibold text-gray-900 dark:text-white text-center mb-2">
					Something went wrong
				</h1>
				<p className="text-gray-600 dark:text-gray-300 text-center mb-6">
					We encountered an unexpected error. Please try refreshing the page.
				</p>
				<div className="flex flex-col space-y-3">
					<button
						onClick={retry}
						className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
					>
						Try Again
					</button>
					<button
						onClick={() => window.location.reload()}
						className="w-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white font-medium py-2 px-4 rounded-md transition-colors"
					>
						Refresh Page
					</button>
				</div>
				{process.env.NODE_ENV === 'development' && (
					<details className="mt-4">
						<summary className="text-sm text-gray-500 cursor-pointer">
							Error Details (Development)
						</summary>
						<pre className="mt-2 text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto">
							{JSON.stringify(error.toLogObject(), null, 2)}
						</pre>
					</details>
				)}
			</div>
		</div>
	);
}

function SectionErrorFallback({ retry }: ErrorFallbackProps) {
	return (
		<div className="bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded-lg p-4">
			<div className="flex items-start">
				<div className="flex-shrink-0">
					<svg
						className="h-5 w-5 text-red-400"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
						/>
					</svg>
				</div>
				<div className="ml-3 flex-1">
					<h3 className="text-sm font-medium text-red-800 dark:text-red-200">
						Section Error
					</h3>
					<p className="mt-1 text-sm text-red-700 dark:text-red-300">
						This section encountered an error and couldn&#39;t load properly.
					</p>
					<div className="mt-3">
						<button
							onClick={retry}
							className="text-sm bg-red-100 hover:bg-red-200 dark:bg-red-800 dark:hover:bg-red-700 text-red-800 dark:text-red-200 font-medium py-1 px-3 rounded transition-colors"
						>
							Retry
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}

function ComponentErrorFallback({ retry }: ErrorFallbackProps) {
	return (
		<div className="bg-yellow-50 dark:bg-yellow-900/10 border border-yellow-200 dark:border-yellow-800 rounded p-3">
			<div className="flex items-center">
				<svg
					className="h-4 w-4 text-yellow-400 mr-2"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
					/>
				</svg>
				<span className="text-sm text-yellow-800 dark:text-yellow-200 flex-1">
					Component failed to load
				</span>
				<button
					onClick={retry}
					className="text-xs bg-yellow-100 hover:bg-yellow-200 dark:bg-yellow-800 dark:hover:bg-yellow-700 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded transition-colors"
				>
					Retry
				</button>
			</div>
		</div>
	);
}
