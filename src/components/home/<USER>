'use client';

import { Translate } from '@/components/ui';
import { Book<PERSON>pen, LayoutGrid, CheckCircle, PenTool } from 'lucide-react';
import { FeatureCard } from './feature-card';

export function PracticeTypesSection() {
	return (
		<div className="space-y-4">
			<h2 className="text-2xl font-bold text-center">
				<Translate text="home.practice_types" />
			</h2>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				<FeatureCard
					icon={BookOpen}
					titleKey="home.flashcards"
					descriptionKey="home.flashcards_description"
				/>
				<FeatureCard
					icon={LayoutGrid}
					titleKey="home.quiz"
					descriptionKey="home.quiz_description"
				/>
				<FeatureCard
					icon={CheckCircle}
					titleKey="home.matching"
					descriptionKey="home.matching_description"
				/>
				<FeatureCard
					icon={PenTool}
					titleKey="home.fill_in_blank"
					descriptionKey="home.fill_in_blank_description"
				/>
			</div>
		</div>
	);
}