'use client';

import { useTranslation } from '@/contexts';

type TranslateProps = {
	text: string;
	className?: string;
	values?: Record<string, string | number>;
};

/**
 * Translate component - Renders translated text based on the current language
 *
 * Usage:
 * <Translate text="nav.home" />
 * <Translate text="headings.collections" className="text-lg font-bold" />
 */
export function Translate({ text, className, values }: TranslateProps) {
	const { t } = useTranslation();

	return <span className={className}>{t(text, values)}</span>;
}

/**
 * This component is used to translate any children text
 * and is useful for conditional rendering or when you need
 * to wrap elements that include translation keys
 *
 * Usage:
 * <TranslationProvider>
 *   <h1>Translated: <TranslateChildren>nav.home</TranslateChildren></h1>
 * </TranslationProvider>
 */
export function TranslateChildren({
	children,
	className,
}: {
	children: string;
	className?: string;
}) {
	const { t } = useTranslation();

	return <span className={className}>{t(children)}</span>;
}

/**
 * TranslateText component - Takes a raw text string and wraps it in a translation key
 * This is useful for directly translating UI text without having to create a translation key
 *
 * Usage:
 * <TranslateText>Submit</TranslateText>
 * <Button><TranslateText>Cancel</TranslateText></Button>
 */
export function TranslateText({ children, className }: { children: string; className?: string }) {
	const { t } = useTranslation();

	// Create a translation key from the text
	// This is useful for components where we don't want to create explicit translation keys
	const textKey = `ui.${children.toLowerCase().replace(/\s+/g, '_')}`;

	return <span className={className}>{t(textKey)}</span>;
}
