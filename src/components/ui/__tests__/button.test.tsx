import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '../button';

describe('Button Component', () => {
	it('renders button with text', () => {
		render(<Button>Click me</Button>);

		expect(screen.getByRole('button')).toBeInTheDocument();
		expect(screen.getByText('Click me')).toBeInTheDocument();
	});

	it('handles click events', () => {
		const handleClick = vi.fn();
		render(<Button onClick={handleClick}>Click me</Button>);

		fireEvent.click(screen.getByRole('button'));

		expect(handleClick).toHaveBeenCalledTimes(1);
	});

	it('applies variant classes correctly', () => {
		render(<Button variant="destructive">Delete</Button>);

		const button = screen.getByRole('button');
		expect(button).toHaveClass('bg-destructive');
	});

	it('applies size classes correctly', () => {
		render(<Button size="sm">Small</Button>);

		const button = screen.getByRole('button');
		expect(button).toHaveClass('min-h-[44px]');
	});

	it('can be disabled', () => {
		render(<Button disabled>Disabled</Button>);

		const button = screen.getByRole('button');
		expect(button).toBeDisabled();
		expect(button).toHaveClass('disabled:pointer-events-none');
	});

	it('renders with custom className', () => {
		render(<Button className="custom-class">Button</Button>);

		const button = screen.getByRole('button');
		expect(button).toHaveClass('custom-class');
	});

	it('forwards ref correctly', () => {
		const ref = vi.fn();
		render(<Button ref={ref}>Button</Button>);

		expect(ref).toHaveBeenCalled();
	});

	it('spreads additional props', () => {
		render(<Button data-testid="custom-button">Button</Button>);

		expect(screen.getByTestId('custom-button')).toBeInTheDocument();
	});
});
