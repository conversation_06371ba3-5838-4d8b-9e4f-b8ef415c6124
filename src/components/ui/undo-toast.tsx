'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Undo2, X, RotateCcw } from 'lucide-react';
import { Button } from './button';
import { cn } from '@/lib';
import { UndoAction, formatUndoDescription } from '@/hooks/use-undo-actions';

// ============================================================================
// TYPES
// ============================================================================

interface UndoToastProps {
	action: UndoAction | null;
	onUndo: (actionId: string) => Promise<boolean>;
	onDismiss: (actionId: string) => void;
	isUndoing?: boolean;
	className?: string;
}

interface UndoToastManagerProps {
	actions: UndoAction[];
	onUndo: (actionId: string) => Promise<boolean>;
	onDismiss: (actionId: string) => void;
	isUndoing?: boolean;
	maxVisible?: number;
	className?: string;
}

// ============================================================================
// UNDO TOAST COMPONENT
// ============================================================================

export function UndoToast({
	action,
	onUndo,
	onDismiss,
	isUndoing = false,
	className,
}: UndoToastProps) {
	const [timeLeft, setTimeLeft] = useState<number | null>(null);
	const [isVisible, setIsVisible] = useState(true);

	// Calculate time left for auto-expire
	useEffect(() => {
		if (!action?.timeout) return;

		const updateTimeLeft = () => {
			const elapsed = Date.now() - action.timestamp;
			const remaining = action.timeout! - elapsed;
			
			if (remaining <= 0) {
				setTimeLeft(0);
				setIsVisible(false);
				return;
			}
			
			setTimeLeft(Math.ceil(remaining / 1000));
		};

		updateTimeLeft();
		const interval = setInterval(updateTimeLeft, 1000);

		return () => clearInterval(interval);
	}, [action]);

	const handleUndo = async () => {
		if (!action || isUndoing) return;
		
		const success = await onUndo(action.id);
		if (success) {
			setIsVisible(false);
		}
	};

	const handleDismiss = () => {
		if (!action) return;
		onDismiss(action.id);
		setIsVisible(false);
	};

	if (!action || !isVisible) return null;

	return (
		<motion.div
			initial={{ opacity: 0, y: 50, scale: 0.95 }}
			animate={{ opacity: 1, y: 0, scale: 1 }}
			exit={{ opacity: 0, y: -20, scale: 0.95 }}
			transition={{ duration: 0.2, ease: 'easeOut' }}
			className={cn(
				'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50',
				'bg-background border border-border rounded-lg shadow-lg',
				'min-w-[320px] max-w-[480px] p-4',
				'flex items-center gap-3',
				className
			)}
		>
			{/* Icon */}
			<div className="flex-shrink-0">
				{isUndoing ? (
					<RotateCcw className="h-5 w-5 text-primary animate-spin" />
				) : (
					<Undo2 className="h-5 w-5 text-primary" />
				)}
			</div>

			{/* Content */}
			<div className="flex-1 min-w-0">
				<p className="text-sm font-medium text-foreground">
					{formatUndoDescription(action)}
				</p>
				{timeLeft !== null && timeLeft > 0 && (
					<p className="text-xs text-muted-foreground mt-1">
						Auto-dismiss in {timeLeft}s
					</p>
				)}
			</div>

			{/* Actions */}
			<div className="flex items-center gap-2 flex-shrink-0">
				<Button
					variant="outline"
					size="sm"
					onClick={handleUndo}
					disabled={isUndoing}
					className="h-8 px-3 text-xs"
				>
					{isUndoing ? 'Undoing...' : 'Undo'}
				</Button>
				
				<Button
					variant="ghost"
					size="sm"
					onClick={handleDismiss}
					disabled={isUndoing}
					className="h-8 w-8 p-0"
				>
					<X className="h-4 w-4" />
				</Button>
			</div>

			{/* Progress bar for timeout */}
			{action.timeout && timeLeft !== null && (
				<div className="absolute bottom-0 left-0 right-0 h-1 bg-border rounded-b-lg overflow-hidden">
					<motion.div
						className="h-full bg-primary"
						initial={{ width: '100%' }}
						animate={{ width: `${(timeLeft / (action.timeout / 1000)) * 100}%` }}
						transition={{ duration: 1, ease: 'linear' }}
					/>
				</div>
			)}
		</motion.div>
	);
}

// ============================================================================
// UNDO TOAST MANAGER
// ============================================================================

export function UndoToastManager({
	actions,
	onUndo,
	onDismiss,
	isUndoing = false,
	maxVisible = 3,
	className,
}: UndoToastManagerProps) {
	// Show only the most recent actions
	const visibleActions = actions.slice(0, maxVisible);

	return (
		<div className={cn('fixed inset-0 pointer-events-none z-50', className)}>
			<AnimatePresence mode="popLayout">
				{visibleActions.map((action, index) => (
					<motion.div
						key={action.id}
						initial={{ opacity: 0, y: 50 }}
						animate={{ 
							opacity: 1, 
							y: 0,
							// Stack multiple toasts
							transform: `translateY(${index * -80}px)`,
						}}
						exit={{ opacity: 0, y: -20 }}
						transition={{ duration: 0.2, delay: index * 0.05 }}
						className="pointer-events-auto"
						style={{
							position: 'fixed',
							bottom: `${16 + index * 80}px`,
							left: '50%',
							transform: 'translateX(-50%)',
							zIndex: 50 - index,
						}}
					>
						<UndoToast
							action={action}
							onUndo={onUndo}
							onDismiss={onDismiss}
							isUndoing={isUndoing}
						/>
					</motion.div>
				))}
			</AnimatePresence>
		</div>
	);
}

// ============================================================================
// UNDO TOAST HOOK
// ============================================================================

export function useUndoToast() {
	const [toasts, setToasts] = useState<UndoAction[]>([]);

	const showUndoToast = (action: UndoAction) => {
		setToasts(prev => [action, ...prev.filter(t => t.id !== action.id)]);
	};

	const hideUndoToast = (actionId: string) => {
		setToasts(prev => prev.filter(t => t.id !== actionId));
	};

	const clearAllToasts = () => {
		setToasts([]);
	};

	return {
		toasts,
		showUndoToast,
		hideUndoToast,
		clearAllToasts,
	};
}

// ============================================================================
// UTILITY COMPONENTS
// ============================================================================

interface QuickUndoButtonProps {
	action: UndoAction | null;
	onUndo: (actionId: string) => Promise<boolean>;
	isUndoing?: boolean;
	className?: string;
}

export function QuickUndoButton({
	action,
	onUndo,
	isUndoing = false,
	className,
}: QuickUndoButtonProps) {
	if (!action) return null;

	const handleUndo = async () => {
		if (isUndoing) return;
		await onUndo(action.id);
	};

	return (
		<Button
			variant="outline"
			size="sm"
			onClick={handleUndo}
			disabled={isUndoing}
			className={cn('gap-2', className)}
		>
			{isUndoing ? (
				<RotateCcw className="h-4 w-4 animate-spin" />
			) : (
				<Undo2 className="h-4 w-4" />
			)}
			{isUndoing ? 'Undoing...' : 'Undo'}
		</Button>
	);
}
