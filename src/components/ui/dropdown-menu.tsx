'use client';

import { cn } from '@/lib';
import { DropdownMenu as DropdownMenuPrimitive } from 'radix-ui';

function DropdownMenu(props: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {
	return <DropdownMenuPrimitive.Root {...props} />;
}

function DropdownMenuTrigger({
	'aria-label': ariaLabel,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger> & {
	'aria-label'?: string;
	'data-state'?: 'open' | 'closed';
}) {
	return (
		<DropdownMenuPrimitive.Trigger
			aria-label={ariaLabel}
			aria-haspopup="menu"
			aria-expanded={props['data-state'] === 'open'}
			{...props}
		/>
	);
}

function DropdownMenuContent({
	className,
	sideOffset = 4,
	'aria-label': ariaLabel,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Content> & {
	sideOffset?: number;
	'aria-label'?: string;
}) {
	return (
		<DropdownMenuPrimitive.Portal>
			<DropdownMenuPrimitive.Content
				sideOffset={sideOffset}
				className={cn(
					'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md',
					'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
					className
				)}
				role="menu"
				aria-orientation="vertical"
				aria-label={ariaLabel || 'Dropdown menu'}
				{...props}
			/>
		</DropdownMenuPrimitive.Portal>
	);
}

function DropdownMenuItem({
	className,
	inset,
	'aria-label': ariaLabel,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {
	inset?: boolean;
	'aria-label'?: string;
}) {
	return (
		<DropdownMenuPrimitive.Item
			className={cn(
				'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
				inset && 'pl-8',
				className
			)}
			role="menuitem"
			aria-label={ariaLabel}
			tabIndex={0}
			{...props}
		/>
	);
}

function DropdownMenuLabel({
	className,
	inset,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {
	inset?: boolean;
}) {
	return (
		<DropdownMenuPrimitive.Label
			className={cn('px-2 py-1.5 text-sm font-semibold', inset && 'pl-8', className)}
			{...props}
		/>
	);
}

function DropdownMenuSeparator({
	className,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {
	return (
		<DropdownMenuPrimitive.Separator
			className={cn('-mx-1 my-1 h-px bg-muted', className)}
			{...props}
		/>
	);
}

export {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
};

