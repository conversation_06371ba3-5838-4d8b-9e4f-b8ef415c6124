'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ModelStats } from '@/lib/api/token-monitor.api';
import { Brain, Zap, DollarSign, Clock, TrendingUp, CheckCircle, AlertTriangle } from 'lucide-react';

interface ModelSelectionStatsProps {
	modelStats: ModelStats;
}

export function ModelSelectionStats({ modelStats }: ModelSelectionStatsProps) {
	const models = modelStats.models;
	const availableModels = modelStats.available;
	const summary = modelStats.summary;

	// Sort models by performance score
	const sortedModels = Object.entries(models).sort((a, b) => {
		const scoreA = (a[1].performance.qualityScore + a[1].performance.reliability) / 2;
		const scoreB = (b[1].performance.qualityScore + b[1].performance.reliability) / 2;
		return scoreB - scoreA;
	});

	const getModelBadgeVariant = (model: string) => {
		if (model.includes('gpt-4o-mini')) return 'default';
		if (model.includes('gpt-4o')) return 'secondary';
		if (model.includes('gpt-4')) return 'outline';
		return 'secondary';
	};

	const getPerformanceColor = (score: number) => {
		if (score >= 0.9) return 'text-green-600';
		if (score >= 0.7) return 'text-yellow-600';
		return 'text-red-600';
	};

	const getCostColor = (cost: number) => {
		if (cost <= 0.001) return 'text-green-600';
		if (cost <= 0.01) return 'text-yellow-600';
		return 'text-red-600';
	};

	return (
		<div className="space-y-4">
			{/* Model Overview */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Available Models</CardTitle>
						<Brain className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{summary.totalModels}</div>
						<p className="text-xs text-muted-foreground">
							Models configured
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Average Cost</CardTitle>
						<DollarSign className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							${summary.averageCost.toFixed(4)}
						</div>
						<p className="text-xs text-muted-foreground">
							Per 1K tokens
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Best Performance</CardTitle>
						<TrendingUp className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{sortedModels.length > 0 && 
								(sortedModels[0][1].performance.qualityScore * 100).toFixed(0)
							}%
						</div>
						<p className="text-xs text-muted-foreground">
							{sortedModels.length > 0 && sortedModels[0][0]}
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Fastest Model</CardTitle>
						<Zap className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">
							{sortedModels.length > 0 && 
								Math.min(...sortedModels.map(([, model]) => model.performance.averageLatency))
							}ms
						</div>
						<p className="text-xs text-muted-foreground">
							{sortedModels.length > 0 && 
								sortedModels.find(([, model]) => 
									model.performance.averageLatency === 
									Math.min(...sortedModels.map(([, m]) => m.performance.averageLatency))
								)?.[0]
							}
						</p>
					</CardContent>
				</Card>
			</div>

			{/* Model Performance Comparison */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center">
						<Brain className="h-5 w-5 mr-2" />
						Model Performance Comparison
					</CardTitle>
					<CardDescription>
						Detailed performance metrics for each available model
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						{sortedModels.map(([modelName, modelData]) => (
							<div key={modelName} className="border rounded-lg p-4 space-y-3">
								<div className="flex items-center justify-between">
									<div className="flex items-center space-x-2">
										<h3 className="font-medium">{modelName}</h3>
										<Badge variant={getModelBadgeVariant(modelName)}>
											{modelData.availability}
										</Badge>
									</div>
									<div className="text-right">
										<div className={`text-sm font-medium ${getCostColor(modelData.costPer1kTokens)}`}>
											${modelData.costPer1kTokens.toFixed(6)}/1K tokens
										</div>
									</div>
								</div>

								<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
									<div className="space-y-2">
										<div className="flex justify-between text-sm">
											<span>Quality Score</span>
											<span className={getPerformanceColor(modelData.performance.qualityScore)}>
												{(modelData.performance.qualityScore * 100).toFixed(0)}%
											</span>
										</div>
										<Progress 
											value={modelData.performance.qualityScore * 100} 
											className="h-2"
										/>
									</div>

									<div className="space-y-2">
										<div className="flex justify-between text-sm">
											<span>Reliability</span>
											<span className={getPerformanceColor(modelData.performance.reliability)}>
												{(modelData.performance.reliability * 100).toFixed(0)}%
											</span>
										</div>
										<Progress 
											value={modelData.performance.reliability * 100} 
											className="h-2"
										/>
									</div>

									<div className="space-y-2">
										<div className="flex justify-between text-sm">
											<span>Avg Latency</span>
											<span className="font-medium">
												{modelData.performance.averageLatency}ms
											</span>
										</div>
										<Progress 
											value={Math.max(0, 100 - (modelData.performance.averageLatency / 50))} 
											className="h-2"
										/>
									</div>
								</div>

								<div className="flex justify-between text-xs text-muted-foreground">
									<span>{modelData.capabilities} capabilities</span>
									<span>Availability: {modelData.availability}</span>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Model Selection Strategy */}
			<Card>
				<CardHeader>
					<CardTitle>Model Selection Strategy</CardTitle>
					<CardDescription>
						How the system chooses the optimal model for each request
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div className="space-y-3">
								<h4 className="font-medium">Selection Criteria</h4>
								<div className="space-y-2 text-sm">
									<div className="flex justify-between">
										<span>Request Complexity</span>
										<span className="font-medium">40%</span>
									</div>
									<div className="flex justify-between">
										<span>Quality Requirements</span>
										<span className="font-medium">30%</span>
									</div>
									<div className="flex justify-between">
										<span>Cost Efficiency</span>
										<span className="font-medium">15%</span>
									</div>
									<div className="flex justify-between">
										<span>Speed Requirements</span>
										<span className="font-medium">10%</span>
									</div>
									<div className="flex justify-between">
										<span>Availability</span>
										<span className="font-medium">5%</span>
									</div>
								</div>
							</div>

							<div className="space-y-3">
								<h4 className="font-medium">Operation Mapping</h4>
								<div className="space-y-2 text-sm">
									<div className="flex justify-between">
										<span>Simple Generation</span>
										<Badge variant="default">gpt-4o-mini</Badge>
									</div>
									<div className="flex justify-between">
										<span>Complex Evaluation</span>
										<Badge variant="secondary">gpt-4o</Badge>
									</div>
									<div className="flex justify-between">
										<span>Creative Tasks</span>
										<Badge variant="secondary">gpt-4o</Badge>
									</div>
									<div className="flex justify-between">
										<span>High Accuracy</span>
										<Badge variant="outline">gpt-4</Badge>
									</div>
								</div>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Model Recommendations */}
			<Card>
				<CardHeader>
					<CardTitle>Model Optimization Recommendations</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						{/* Check for underperforming models */}
						{sortedModels.some(([, model]) => model.performance.reliability < 0.9) && (
							<div className="flex items-start space-x-3 p-3 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
								<AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-yellow-800 dark:text-yellow-200">
										Low Reliability Detected
									</h4>
									<p className="text-sm text-yellow-700 dark:text-yellow-300">
										Some models have reliability below 90%. Consider monitoring their performance more closely.
									</p>
								</div>
							</div>
						)}

						{/* Check for high latency */}
						{sortedModels.some(([, model]) => model.performance.averageLatency > 3000) && (
							<div className="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
								<Clock className="h-5 w-5 text-blue-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-blue-800 dark:text-blue-200">
										High Latency Models
									</h4>
									<p className="text-sm text-blue-700 dark:text-blue-300">
										Some models have high average latency. Consider using faster models for time-sensitive operations.
									</p>
								</div>
							</div>
						)}

						{/* Check for cost optimization */}
						{summary.averageCost > 0.01 && (
							<div className="flex items-start space-x-3 p-3 bg-purple-50 dark:bg-purple-950 rounded-lg">
								<DollarSign className="h-5 w-5 text-purple-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-purple-800 dark:text-purple-200">
										Cost Optimization Opportunity
									</h4>
									<p className="text-sm text-purple-700 dark:text-purple-300">
										Average model cost is high. Consider using more cost-effective models for simple operations.
									</p>
								</div>
							</div>
						)}

						{/* All good */}
						{sortedModels.every(([, model]) => 
							model.performance.reliability >= 0.9 && 
							model.performance.averageLatency <= 3000
						) && summary.averageCost <= 0.01 && (
							<div className="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-950 rounded-lg">
								<CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
								<div>
									<h4 className="font-medium text-green-800 dark:text-green-200">
										Optimal Model Configuration
									</h4>
									<p className="text-sm text-green-700 dark:text-green-300">
										All models are performing well with good reliability, reasonable latency, and cost-effective pricing.
									</p>
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
