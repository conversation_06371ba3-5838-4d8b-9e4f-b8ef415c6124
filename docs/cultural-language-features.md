# Cultural & Language Features - Development Plan

## Overview
Implement comprehensive cultural and linguistic features including cultural notes, regional variations, idiomatic expressions, and contextual language understanding to provide rich, culturally-aware language learning experiences.

## Technical Architecture

### Core Components

#### 1. Cultural Context Engine
- **Location**: `src/backend/services/cultural-context.service.ts`
- **Purpose**: Manage cultural information and context-aware content
- **Features**:
  - Cultural note generation
  - Context-sensitive explanations
  - Cultural appropriateness checking
  - Cross-cultural comparison

#### 2. Regional Variation Service
- **Location**: `src/backend/services/regional-variation.service.ts`
- **Purpose**: Handle regional language differences and variations
- **Features**:
  - Dialect management
  - Regional pronunciation guides
  - Local usage patterns
  - Geographic context mapping

#### 3. Idiomatic Expression Engine
- **Location**: `src/backend/services/idiom.service.ts`
- **Purpose**: Manage idiomatic expressions and figurative language
- **Features**:
  - Idiom detection and explanation
  - Cultural context for expressions
  - Usage examples and scenarios
  - Difficulty assessment

## Database Schema Extensions

### New Tables

```prisma
model CulturalNote {
  id              String   @id @default(uuid())
  content_id      String   // Related word, phrase, or concept
  content_type    String   // 'word', 'phrase', 'concept', 'grammar'
  culture_code    String   // 'US', 'UK', 'VN', 'JP', etc.
  note_type       String   // 'usage', 'etiquette', 'history', 'taboo', 'formal_informal'
  title           String
  description     String
  examples        Json     // Usage examples with cultural context
  importance_level String  @default("medium") // 'low', 'medium', 'high', 'critical'
  source          String?  // Source of cultural information
  verified_by     String?  // Expert who verified the information
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  @@index([content_id, content_type])
  @@index([culture_code])
  @@index([note_type])
  @@index([importance_level])
}

model RegionalVariation {
  id              String   @id @default(uuid())
  base_content_id String   // Original word/phrase
  content_type    String
  region_code     String   // 'US_SOUTH', 'UK_LONDON', 'VN_NORTH', etc.
  country_code    String   // 'US', 'UK', 'VN', etc.
  variation_type  String   // 'pronunciation', 'spelling', 'meaning', 'usage'
  variation_text  String   // The regional variation
  pronunciation   String?  // IPA or phonetic representation
  audio_url       String?  // Audio pronunciation
  usage_frequency String   @default("common") // 'rare', 'uncommon', 'common', 'very_common'
  formality_level String   @default("neutral") // 'very_formal', 'formal', 'neutral', 'informal', 'slang'
  examples        Json     // Examples of usage in context
  notes           String?  // Additional notes about the variation
  created_at      DateTime @default(now())
  
  @@index([base_content_id, content_type])
  @@index([region_code])
  @@index([country_code])
  @@index([variation_type])
}

model IdiomaticExpression {
  id              String   @id @default(uuid())
  expression      String   // The idiomatic expression
  language        String   // 'EN', 'VI'
  literal_meaning String   // Word-for-word translation
  actual_meaning  String   // What it actually means
  cultural_context String  // Cultural background and context
  origin_story    String?  // Etymology or origin story
  usage_examples  Json     // Examples in different contexts
  equivalent_expressions Json? // Similar expressions in other languages/cultures
  difficulty_level String  @default("intermediate") // 'beginner', 'intermediate', 'advanced'
  frequency       String   @default("common") // How often it's used
  formality       String   @default("neutral") // Formality level
  regions         String[] // Where this expression is commonly used
  tags            String[] // Categorization tags
  audio_url       String?  // Pronunciation audio
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  @@index([language])
  @@index([difficulty_level])
  @@index([frequency])
  @@index([formality])
}

model CulturalContext {
  id              String   @id @default(uuid())
  context_type    String   // 'business', 'social', 'family', 'academic', 'religious'
  culture_code    String
  context_name    String   // Specific context name
  description     String
  communication_style Json // How communication works in this context
  etiquette_rules Json     // Do's and don'ts
  common_phrases  Json     // Phrases commonly used in this context
  taboos          Json     // Things to avoid
  examples        Json     // Real-world examples
  importance_level String  @default("medium")
  created_at      DateTime @default(now())
  
  @@index([context_type])
  @@index([culture_code])
  @@index([importance_level])
}

model LanguageRegister {
  id              String   @id @default(uuid())
  content_id      String
  content_type    String
  register_type   String   // 'formal', 'informal', 'academic', 'business', 'casual', 'slang'
  register_text   String   // How to say it in this register
  context_usage   String   // When to use this register
  examples        Json     // Examples of usage
  cultural_notes  String?  // Cultural considerations
  appropriateness Json     // When it's appropriate/inappropriate
  created_at      DateTime @default(now())
  
  @@index([content_id, content_type])
  @@index([register_type])
}

model CrossCulturalComparison {
  id              String   @id @default(uuid())
  concept         String   // The concept being compared
  culture_a_code  String
  culture_b_code  String
  culture_a_approach String // How culture A handles this concept
  culture_b_approach String // How culture B handles this concept
  key_differences Json     // Main differences
  similarities    Json     // What's similar
  learning_tips   Json     // Tips for learners
  common_mistakes Json     // Common cross-cultural mistakes
  created_at      DateTime @default(now())
  
  @@index([concept])
  @@index([culture_a_code, culture_b_code])
}

model CulturalLearningPath {
  id              String   @id @default(uuid())
  user_id         String
  target_culture  String   // Culture the user wants to learn about
  current_level   String   @default("beginner") // Cultural awareness level
  completed_topics String[] // Topics already covered
  recommended_topics String[] // Next recommended topics
  learning_goals  Json     // User's cultural learning objectives
  progress_data   Json     // Detailed progress information
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([target_culture])
}

model UserCulturalProfile {
  id              String   @id @default(uuid())
  user_id         String   @unique
  native_culture  String   // User's native culture
  target_cultures String[] // Cultures they're learning about
  cultural_sensitivity_level String @default("medium") // How much cultural context they want
  preferred_regions String[] // Preferred regional variations
  avoided_topics  String[] // Cultural topics they want to avoid
  learning_style  String   @default("gradual") // 'immersive', 'gradual', 'comparative'
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([native_culture])
}

model CulturalFeedback {
  id              String   @id @default(uuid())
  user_id         String
  content_id      String
  content_type    String   // 'cultural_note', 'regional_variation', 'idiom'
  feedback_type   String   // 'helpful', 'not_helpful', 'incorrect', 'offensive'
  feedback_text   String?
  cultural_context String? // User's cultural perspective
  created_at      DateTime @default(now())
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([content_id, content_type])
  @@index([feedback_type])
}
```

## Implementation Plan

### Phase 1: Cultural Context Foundation (Week 1-2)

#### 1.1 Cultural Context Engine
```typescript
// src/backend/services/cultural-context.service.ts
export interface CulturalContextService {
  getCulturalNotes(
    contentId: string,
    contentType: string,
    userCulture?: string
  ): Promise<CulturalNote[]>;
  
  generateCulturalExplanation(
    content: string,
    targetCulture: string,
    userCulture: string
  ): Promise<CulturalExplanation>;
  
  checkCulturalAppropriateness(
    content: string,
    context: CulturalContext,
    targetAudience: string
  ): Promise<AppropriatenessCheck>;
  
  suggestCulturalAlternatives(
    content: string,
    fromCulture: string,
    toCulture: string
  ): Promise<CulturalAlternative[]>;
}

interface CulturalExplanation {
  explanation: string;
  importance: 'low' | 'medium' | 'high' | 'critical';
  examples: CulturalExample[];
  doAndDonts: {
    dos: string[];
    donts: string[];
  };
  crossCulturalTips: string[];
}

class CulturalContextEngine {
  async generateCulturalNote(
    contentId: string,
    contentType: string,
    targetCulture: string,
    userCulture?: string
  ): Promise<CulturalNote> {
    const content = await this.getContent(contentId, contentType);
    const culturalData = await this.getCulturalData(targetCulture);
    
    // Analyze cultural significance
    const significance = await this.analyzeCulturalSignificance(
      content,
      culturalData
    );
    
    // Generate appropriate explanation
    const explanation = await this.generateExplanation(
      content,
      significance,
      targetCulture,
      userCulture
    );
    
    // Create examples
    const examples = await this.generateCulturalExamples(
      content,
      targetCulture,
      significance.contexts
    );
    
    return {
      content_id: contentId,
      content_type: contentType,
      culture_code: targetCulture,
      note_type: significance.type,
      title: explanation.title,
      description: explanation.description,
      examples,
      importance_level: significance.importance,
      source: 'ai_generated'
    };
  }
  
  private async analyzeCulturalSignificance(
    content: any,
    culturalData: CulturalData
  ): Promise<CulturalSignificance> {
    // Use NLP and cultural knowledge base to analyze
    const culturalMarkers = await this.extractCulturalMarkers(content);
    const contextualRelevance = await this.assessContextualRelevance(
      culturalMarkers,
      culturalData
    );
    
    return {
      type: this.determineCulturalNoteType(culturalMarkers),
      importance: this.calculateImportance(contextualRelevance),
      contexts: this.identifyRelevantContexts(culturalMarkers, culturalData),
      sensitivities: this.identifySensitivities(culturalMarkers, culturalData)
    };
  }
  
  private async generateExplanation(
    content: any,
    significance: CulturalSignificance,
    targetCulture: string,
    userCulture?: string
  ): Promise<{ title: string; description: string }> {
    const prompt = this.buildCulturalExplanationPrompt(
      content,
      significance,
      targetCulture,
      userCulture
    );
    
    const response = await this.llmService.generateText(prompt);
    
    return {
      title: this.extractTitle(response),
      description: this.extractDescription(response)
    };
  }
}
```

#### 1.2 Cross-Cultural Comparison System
```typescript
class CrossCulturalComparator {
  async generateComparison(
    concept: string,
    cultureA: string,
    cultureB: string
  ): Promise<CrossCulturalComparison> {
    const [approachA, approachB] = await Promise.all([
      this.getCulturalApproach(concept, cultureA),
      this.getCulturalApproach(concept, cultureB)
    ]);
    
    const differences = this.identifyDifferences(approachA, approachB);
    const similarities = this.identifySimilarities(approachA, approachB);
    const learningTips = await this.generateLearningTips(differences, similarities);
    const commonMistakes = await this.identifyCommonMistakes(cultureA, cultureB, concept);
    
    return {
      concept,
      culture_a_code: cultureA,
      culture_b_code: cultureB,
      culture_a_approach: approachA.description,
      culture_b_approach: approachB.description,
      key_differences: differences,
      similarities,
      learning_tips: learningTips,
      common_mistakes: commonMistakes
    };
  }
  
  private async getCulturalApproach(
    concept: string,
    culture: string
  ): Promise<CulturalApproach> {
    // Query cultural knowledge base
    const culturalData = await this.culturalKnowledgeBase.query({
      concept,
      culture,
      aspects: ['communication', 'behavior', 'values', 'etiquette']
    });
    
    return {
      description: culturalData.description,
      communicationStyle: culturalData.communication,
      behavioralNorms: culturalData.behavior,
      values: culturalData.values,
      etiquette: culturalData.etiquette
    };
  }
}
```

### Phase 2: Regional Variation System (Week 3-4)

#### 2.1 Regional Variation Engine
```typescript
// src/backend/services/regional-variation.service.ts
export interface RegionalVariationService {
  getRegionalVariations(
    contentId: string,
    contentType: string,
    userRegion?: string
  ): Promise<RegionalVariation[]>;
  
  detectRegionalUsage(
    text: string,
    language: string
  ): Promise<RegionalUsageDetection>;
  
  suggestRegionalAlternatives(
    content: string,
    fromRegion: string,
    toRegion: string
  ): Promise<RegionalAlternative[]>;
  
  validateRegionalAppropriateness(
    content: string,
    targetRegion: string,
    context: string
  ): Promise<RegionalAppropriatenessCheck>;
}

class RegionalVariationEngine {
  async detectAndCatalogVariations(
    baseContent: string,
    language: string
  ): Promise<RegionalVariation[]> {
    const regions = await this.getLanguageRegions(language);
    const variations: RegionalVariation[] = [];
    
    for (const region of regions) {
      const regionVariations = await this.detectRegionSpecificVariations(
        baseContent,
        region
      );
      
      for (const variation of regionVariations) {
        const validatedVariation = await this.validateVariation(
          baseContent,
          variation,
          region
        );
        
        if (validatedVariation.isValid) {
          variations.push({
            base_content_id: baseContent,
            content_type: 'word', // or determined dynamically
            region_code: region.code,
            country_code: region.country,
            variation_type: variation.type,
            variation_text: variation.text,
            pronunciation: variation.pronunciation,
            usage_frequency: validatedVariation.frequency,
            formality_level: validatedVariation.formality,
            examples: await this.generateRegionalExamples(variation, region),
            notes: validatedVariation.notes
          });
        }
      }
    }
    
    return variations;
  }
  
  private async detectRegionSpecificVariations(
    content: string,
    region: Region
  ): Promise<VariationCandidate[]> {
    // Use regional dictionaries and corpora
    const regionalData = await this.getRegionalData(region);
    const variations: VariationCandidate[] = [];
    
    // Check pronunciation variations
    const pronunciationVariations = await this.detectPronunciationVariations(
      content,
      regionalData.phonetics
    );
    variations.push(...pronunciationVariations);
    
    // Check spelling variations
    const spellingVariations = await this.detectSpellingVariations(
      content,
      regionalData.orthography
    );
    variations.push(...spellingVariations);
    
    // Check meaning variations
    const meaningVariations = await this.detectMeaningVariations(
      content,
      regionalData.semantics
    );
    variations.push(...meaningVariations);
    
    // Check usage variations
    const usageVariations = await this.detectUsageVariations(
      content,
      regionalData.pragmatics
    );
    variations.push(...usageVariations);
    
    return variations;
  }
  
  private async generateRegionalExamples(
    variation: VariationCandidate,
    region: Region
  ): Promise<RegionalExample[]> {
    const examples: RegionalExample[] = [];
    
    // Generate context-appropriate examples
    const contexts = ['casual', 'formal', 'business', 'academic'];
    
    for (const context of contexts) {
      const example = await this.generateContextualExample(
        variation,
        region,
        context
      );
      
      if (example) {
        examples.push({
          context,
          example: example.text,
          translation: example.translation,
          notes: example.culturalNotes
        });
      }
    }
    
    return examples;
  }
}
```

### Phase 3: Idiomatic Expression System (Week 5-6)

#### 3.1 Idiom Detection and Management
```typescript
// src/backend/services/idiom.service.ts
export interface IdiomService {
  detectIdioms(text: string, language: string): Promise<IdiomDetection[]>;
  explainIdiom(idiomId: string, userCulture?: string): Promise<IdiomExplanation>;
  findEquivalentIdioms(idiomId: string, targetLanguage: string): Promise<EquivalentIdiom[]>;
  generateIdiomExercises(idiomId: string, difficulty: string): Promise<IdiomExercise[]>;
  assessIdiomDifficulty(idiom: string, userLevel: string): Promise<DifficultyAssessment>;
}

class IdiomDetectionEngine {
  private idiomPatterns: Map<string, RegExp[]> = new Map();
  private idiomDatabase: IdiomDatabase;
  
  async detectIdioms(text: string, language: string): Promise<IdiomDetection[]> {
    const detections: IdiomDetection[] = [];
    
    // Get language-specific idiom patterns
    const patterns = this.idiomPatterns.get(language) || [];
    
    // Pattern-based detection
    for (const pattern of patterns) {
      const matches = text.match(pattern);
      if (matches) {
        for (const match of matches) {
          const idiom = await this.validateIdiom(match, language);
          if (idiom) {
            detections.push({
              text: match,
              startIndex: text.indexOf(match),
              endIndex: text.indexOf(match) + match.length,
              idiomId: idiom.id,
              confidence: idiom.confidence
            });
          }
        }
      }
    }
    
    // Semantic-based detection using embeddings
    const semanticDetections = await this.detectIdiomsSemanticaly(text, language);
    detections.push(...semanticDetections);
    
    // Remove duplicates and sort by confidence
    return this.deduplicateAndSort(detections);
  }
  
  async explainIdiom(
    idiomId: string,
    userCulture?: string
  ): Promise<IdiomExplanation> {
    const idiom = await this.idiomDatabase.getIdiom(idiomId);
    
    const explanation: IdiomExplanation = {
      idiom: idiom.expression,
      literalMeaning: idiom.literal_meaning,
      actualMeaning: idiom.actual_meaning,
      culturalContext: idiom.cultural_context,
      originStory: idiom.origin_story,
      usageExamples: await this.generateUsageExamples(idiom),
      difficulty: idiom.difficulty_level,
      formality: idiom.formality,
      frequency: idiom.frequency
    };
    
    // Add cross-cultural explanation if user culture is different
    if (userCulture && userCulture !== this.extractCultureFromIdiom(idiom)) {
      explanation.crossCulturalNotes = await this.generateCrossCulturalNotes(
        idiom,
        userCulture
      );
      explanation.equivalentExpressions = await this.findCulturalEquivalents(
        idiom,
        userCulture
      );
    }
    
    return explanation;
  }
  
  private async generateUsageExamples(idiom: IdiomaticExpression): Promise<UsageExample[]> {
    const examples: UsageExample[] = [];
    const contexts = ['casual', 'formal', 'business', 'storytelling'];
    
    for (const context of contexts) {
      const example = await this.generateContextualExample(idiom, context);
      if (example) {
        examples.push({
          context,
          sentence: example.sentence,
          situation: example.situation,
          explanation: example.explanation
        });
      }
    }
    
    return examples;
  }
  
  async findCulturalEquivalents(
    idiom: IdiomaticExpression,
    targetCulture: string
  ): Promise<CulturalEquivalent[]> {
    // Find idioms with similar meanings in target culture
    const semanticVector = await this.getIdiomSemanticVector(idiom);
    const targetIdioms = await this.getIdiomsForCulture(targetCulture);
    
    const equivalents: CulturalEquivalent[] = [];
    
    for (const targetIdiom of targetIdioms) {
      const targetVector = await this.getIdiomSemanticVector(targetIdiom);
      const similarity = this.calculateSemanticSimilarity(semanticVector, targetVector);
      
      if (similarity > 0.7) { // High similarity threshold
        equivalents.push({
          expression: targetIdiom.expression,
          similarity,
          culturalNotes: await this.generateEquivalenceNotes(idiom, targetIdiom),
          usageComparison: await this.compareUsage(idiom, targetIdiom)
        });
      }
    }
    
    return equivalents.sort((a, b) => b.similarity - a.similarity);
  }
}
```

### Phase 4: Adaptive Cultural Learning (Week 7-8)

#### 4.1 Personalized Cultural Learning Paths
```typescript
class CulturalLearningPathGenerator {
  async generateLearningPath(
    userId: string,
    targetCulture: string,
    userGoals: CulturalLearningGoals
  ): Promise<CulturalLearningPath> {
    const userProfile = await this.getUserCulturalProfile(userId);
    const currentLevel = await this.assessCulturalAwareness(userId, targetCulture);
    
    // Generate topic sequence based on importance and user level
    const topicSequence = await this.generateTopicSequence(
      targetCulture,
      currentLevel,
      userGoals
    );
    
    // Create personalized learning path
    const learningPath = await this.prisma.culturalLearningPath.create({
      data: {
        user_id: userId,
        target_culture: targetCulture,
        current_level: currentLevel,
        completed_topics: [],
        recommended_topics: topicSequence.slice(0, 5), // Next 5 topics
        learning_goals: userGoals,
        progress_data: {
          totalTopics: topicSequence.length,
          estimatedDuration: this.calculateEstimatedDuration(topicSequence),
          difficultyProgression: this.analyzeDifficultyProgression(topicSequence)
        }
      }
    });
    
    return learningPath;
  }
  
  private async generateTopicSequence(
    culture: string,
    userLevel: string,
    goals: CulturalLearningGoals
  ): Promise<CulturalTopic[]> {
    const allTopics = await this.getCulturalTopics(culture);
    
    // Filter topics based on user level and goals
    const relevantTopics = allTopics.filter(topic => 
      this.isTopicRelevant(topic, userLevel, goals)
    );
    
    // Sort by importance and learning progression
    const sortedTopics = relevantTopics.sort((a, b) => {
      const importanceScore = this.calculateImportanceScore(a, goals) - 
                             this.calculateImportanceScore(b, goals);
      const progressionScore = this.calculateProgressionScore(a, userLevel) - 
                              this.calculateProgressionScore(b, userLevel);
      
      return importanceScore + progressionScore;
    });
    
    return sortedTopics;
  }
  
  async adaptLearningPath(
    userId: string,
    pathId: string,
    userFeedback: CulturalFeedback[]
  ): Promise<CulturalLearningPath> {
    const currentPath = await this.getCulturalLearningPath(pathId);
    const userProgress = await this.analyzeCulturalProgress(userId, userFeedback);
    
    // Adjust difficulty based on user performance
    const adjustedDifficulty = this.adjustDifficultyLevel(
      currentPath.current_level,
      userProgress
    );
    
    // Update recommended topics based on feedback
    const updatedTopics = await this.updateRecommendedTopics(
      currentPath,
      userFeedback,
      adjustedDifficulty
    );
    
    return await this.prisma.culturalLearningPath.update({
      where: { id: pathId },
      data: {
        current_level: adjustedDifficulty,
        recommended_topics: updatedTopics,
        progress_data: {
          ...currentPath.progress_data,
          lastUpdated: new Date(),
          adaptationReason: this.generateAdaptationReason(userFeedback)
        }
      }
    });
  }
}
```

## Frontend Integration

### Cultural Learning Dashboard
```typescript
// src/components/ui/cultural-learning-dashboard.tsx
export function CulturalLearningDashboard({ userId }: { userId: string }) {
  const { culturalProfile, learningPath } = useCulturalLearning(userId);
  const { culturalNotes, regionalVariations, idioms } = useCulturalContent();
  
  return (
    <div className="cultural-learning-dashboard">
      <div className="dashboard-header">
        <h2>Cultural Learning Journey</h2>
        <CulturalProgressIndicator progress={learningPath?.progress_data} />
      </div>
      
      <div className="cultural-content-grid">
        <CulturalNotesPanel notes={culturalNotes} />
        <RegionalVariationsPanel variations={regionalVariations} />
        <IdiomaticExpressionsPanel idioms={idioms} />
        <CrossCulturalComparisons userId={userId} />
      </div>
      
      <div className="learning-path-section">
        <h3>Your Cultural Learning Path</h3>
        <CulturalTopicSequence path={learningPath} />
      </div>
    </div>
  );
}
```

### Cultural Context Widget
```typescript
// src/components/ui/cultural-context-widget.tsx
export function CulturalContextWidget({ 
  contentId, 
  contentType, 
  userCulture 
}: {
  contentId: string;
  contentType: string;
  userCulture?: string;
}) {
  const { culturalNotes, loading } = useCulturalNotes(contentId, contentType);
  const { regionalVariations } = useRegionalVariations(contentId, contentType);
  
  if (loading || (!culturalNotes.length && !regionalVariations.length)) {
    return null;
  }
  
  return (
    <div className="cultural-context-widget">
      <div className="widget-header">
        <CulturalIcon />
        <span>Cultural Context</span>
      </div>
      
      <div className="cultural-content">
        {culturalNotes.map(note => (
          <CulturalNoteCard key={note.id} note={note} />
        ))}
        
        {regionalVariations.map(variation => (
          <RegionalVariationCard key={variation.id} variation={variation} />
        ))}
      </div>
    </div>
  );
}
```

## Success Criteria

### Cultural Awareness
- 80%+ user satisfaction with cultural explanations
- 70% improvement in cultural appropriateness understanding
- 60% increase in cross-cultural communication confidence
- 90% accuracy in cultural note relevance

### Regional Understanding
- 85% accuracy in regional variation detection
- 75% user preference for region-specific content
- 80% improvement in regional accent recognition
- 70% better understanding of regional usage patterns

### Idiomatic Comprehension
- 90% accuracy in idiom detection
- 80% improvement in figurative language understanding
- 75% success rate in idiom usage exercises
- 85% user satisfaction with cultural equivalents

## Timeline

- **Week 1-2**: Cultural context foundation and note generation
- **Week 3-4**: Regional variation detection and management
- **Week 5-6**: Idiomatic expression engine and explanation system
- **Week 7-8**: Adaptive cultural learning paths and personalization
- **Week 9**: Testing and cultural expert validation
- **Week 10**: Deployment and community feedback integration
