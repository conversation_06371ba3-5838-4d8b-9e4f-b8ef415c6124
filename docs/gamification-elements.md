# Gamification Elements - Development Plan

## Overview
Implement comprehensive gamification features including badges, rewards, learning streaks, leaderboards, study groups, peer challenges, and community content contributions to enhance user engagement and motivation.

## Technical Architecture

### Core Components

#### 1. Gamification Engine
- **Location**: `src/backend/services/gamification.service.ts`
- **Purpose**: Core gamification logic and rule processing
- **Features**:
  - Achievement tracking
  - Point calculation
  - Level progression
  - Reward distribution

#### 2. Social Features Service
- **Location**: `src/backend/services/social.service.ts`
- **Purpose**: Community and social interaction features
- **Features**:
  - Study groups
  - Peer challenges
  - Community contributions
  - Social leaderboards

#### 3. Reward System
- **Location**: `src/backend/services/reward.service.ts`
- **Purpose**: Manage rewards, badges, and incentives
- **Features**:
  - Badge creation and awarding
  - Virtual currency system
  - Reward redemption
  - Special events

## Database Schema Extensions

### New Tables

```prisma
model UserProfile {
  id              String   @id @default(uuid())
  user_id         String   @unique
  display_name    String
  avatar_url      String?
  level           Int      @default(1)
  experience_points Int    @default(0)
  total_points    Int      @default(0)
  current_streak  Int      @default(0)
  longest_streak  Int      @default(0)
  badges_count    Int      @default(0)
  rank            Int?
  title           String?  // "Vocabulary Master", "Grammar Guru", etc.
  bio             String?
  is_public       Boolean  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([level])
  @@index([total_points])
}

model Badge {
  id              String   @id @default(uuid())
  name            String   @unique
  description     String
  icon_url        String
  category        String   // 'achievement', 'milestone', 'special', 'social'
  rarity          String   // 'common', 'rare', 'epic', 'legendary'
  points_value    Int      @default(0)
  requirements    Json     // Conditions to earn the badge
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  
  @@index([category])
  @@index([rarity])
}

model UserBadge {
  id          String   @id @default(uuid())
  user_id     String
  badge_id    String
  earned_at   DateTime @default(now())
  progress    Json?    // Progress towards earning (for progressive badges)
  is_featured Boolean  @default(false)
  
  user User @relation(fields: [user_id], references: [id])
  badge Badge @relation(fields: [badge_id], references: [id])
  
  @@unique([user_id, badge_id])
  @@index([user_id])
  @@index([earned_at])
}

model Leaderboard {
  id              String   @id @default(uuid())
  name            String
  description     String
  type            String   // 'global', 'weekly', 'monthly', 'friends', 'study_group'
  metric          String   // 'points', 'streak', 'words_learned', 'accuracy'
  time_period     String   // 'all_time', 'weekly', 'monthly', 'daily'
  is_active       Boolean  @default(true)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  @@index([type])
  @@index([metric])
  @@index([time_period])
}

model LeaderboardEntry {
  id              String   @id @default(uuid())
  leaderboard_id  String
  user_id         String
  rank            Int
  score           Float
  previous_rank   Int?
  rank_change     Int      @default(0) // +/- change from previous period
  calculated_at   DateTime @default(now())
  
  leaderboard Leaderboard @relation(fields: [leaderboard_id], references: [id])
  user User @relation(fields: [user_id], references: [id])
  
  @@unique([leaderboard_id, user_id])
  @@index([leaderboard_id, rank])
  @@index([user_id])
}

model StudyGroup {
  id              String   @id @default(uuid())
  name            String
  description     String
  creator_id      String
  is_public       Boolean  @default(true)
  max_members     Int      @default(50)
  current_members Int      @default(1)
  group_code      String   @unique
  group_image_url String?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  creator User @relation(fields: [creator_id], references: [id])
  
  @@index([creator_id])
  @@index([is_public])
  @@index([group_code])
}

model StudyGroupMember {
  id            String   @id @default(uuid())
  group_id      String
  user_id       String
  role          String   @default("member") // 'admin', 'moderator', 'member'
  joined_at     DateTime @default(now())
  contribution_score Int @default(0)
  is_active     Boolean  @default(true)
  
  group StudyGroup @relation(fields: [group_id], references: [id])
  user User @relation(fields: [user_id], references: [id])
  
  @@unique([group_id, user_id])
  @@index([group_id])
  @@index([user_id])
}

model Challenge {
  id              String   @id @default(uuid())
  title           String
  description     String
  type            String   // 'individual', 'group', 'peer_vs_peer'
  category        String   // 'vocabulary', 'grammar', 'reading', 'mixed'
  difficulty      String   // 'beginner', 'intermediate', 'advanced'
  duration        Int      // Duration in days
  target_metric   String   // 'words_learned', 'accuracy', 'streak', 'points'
  target_value    Float    // Target value to achieve
  reward_points   Int      @default(0)
  reward_badge_id String?
  start_date      DateTime
  end_date        DateTime
  is_active       Boolean  @default(true)
  created_by      String?
  max_participants Int?
  
  reward_badge Badge? @relation(fields: [reward_badge_id], references: [id])
  creator User? @relation(fields: [created_by], references: [id])
  
  @@index([type])
  @@index([category])
  @@index([start_date, end_date])
  @@index([is_active])
}

model ChallengeParticipant {
  id              String   @id @default(uuid())
  challenge_id    String
  user_id         String
  joined_at       DateTime @default(now())
  current_progress Float   @default(0.0)
  is_completed    Boolean  @default(false)
  completed_at    DateTime?
  final_score     Float?
  rank            Int?
  
  challenge Challenge @relation(fields: [challenge_id], references: [id])
  user User @relation(fields: [user_id], references: [id])
  
  @@unique([challenge_id, user_id])
  @@index([challenge_id])
  @@index([user_id])
  @@index([is_completed])
}

model CommunityContent {
  id              String   @id @default(uuid())
  creator_id      String
  content_type    String   // 'word_list', 'study_set', 'quiz', 'tip'
  title           String
  description     String
  content_data    Json     // Actual content
  difficulty      String   // 'beginner', 'intermediate', 'advanced'
  language        String   // 'EN', 'VI'
  tags            String[] // Array of tags
  likes_count     Int      @default(0)
  downloads_count Int      @default(0)
  rating          Float?   // Average rating
  rating_count    Int      @default(0)
  is_featured     Boolean  @default(false)
  is_approved     Boolean  @default(false)
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
  
  creator User @relation(fields: [creator_id], references: [id])
  
  @@index([creator_id])
  @@index([content_type])
  @@index([difficulty])
  @@index([language])
  @@index([is_featured])
  @@index([is_approved])
}

model ContentInteraction {
  id              String   @id @default(uuid())
  user_id         String
  content_id      String
  interaction_type String  // 'like', 'download', 'rating', 'comment'
  value           Float?   // For ratings
  comment         String?  // For comments
  created_at      DateTime @default(now())
  
  user User @relation(fields: [user_id], references: [id])
  content CommunityContent @relation(fields: [content_id], references: [id])
  
  @@unique([user_id, content_id, interaction_type])
  @@index([user_id])
  @@index([content_id])
  @@index([interaction_type])
}

model VirtualCurrency {
  id              String   @id @default(uuid())
  user_id         String
  currency_type   String   // 'coins', 'gems', 'tokens'
  balance         Int      @default(0)
  total_earned    Int      @default(0)
  total_spent     Int      @default(0)
  last_updated    DateTime @default(now())
  
  user User @relation(fields: [user_id], references: [id])
  
  @@unique([user_id, currency_type])
  @@index([user_id])
}

model Transaction {
  id              String   @id @default(uuid())
  user_id         String
  transaction_type String  // 'earn', 'spend', 'bonus', 'penalty'
  currency_type   String
  amount          Int
  reason          String   // Description of transaction
  reference_id    String?  // Reference to related entity
  created_at      DateTime @default(now())
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([transaction_type])
  @@index([created_at])
}
```

## Implementation Plan

### Phase 1: Core Gamification System (Week 1-2)

#### 1.1 Gamification Engine
```typescript
// src/backend/services/gamification.service.ts
export interface GamificationService {
  calculatePoints(userId: string, action: GameAction): Promise<PointsResult>;
  updateUserLevel(userId: string): Promise<LevelUpdate>;
  checkBadgeEligibility(userId: string, action: GameAction): Promise<Badge[]>;
  updateStreak(userId: string, action: GameAction): Promise<StreakUpdate>;
  processAchievement(userId: string, achievement: Achievement): Promise<void>;
}

interface GameAction {
  type: ActionType;
  data: Record<string, any>;
  timestamp: Date;
  context?: ActionContext;
}

enum ActionType {
  WORD_LEARNED = 'word_learned',
  EXERCISE_COMPLETED = 'exercise_completed',
  STREAK_MAINTAINED = 'streak_maintained',
  PERFECT_SCORE = 'perfect_score',
  STUDY_SESSION = 'study_session',
  SOCIAL_INTERACTION = 'social_interaction',
  CONTENT_CREATED = 'content_created'
}

class PointsCalculator {
  calculatePoints(action: GameAction, userProfile: UserProfile): number {
    const basePoints = this.getBasePoints(action.type);
    const multipliers = this.getMultipliers(action, userProfile);
    
    let totalPoints = basePoints;
    
    // Apply multipliers
    for (const multiplier of multipliers) {
      totalPoints *= multiplier.value;
    }
    
    // Apply bonuses
    const bonuses = this.getBonuses(action, userProfile);
    for (const bonus of bonuses) {
      totalPoints += bonus.value;
    }
    
    return Math.round(totalPoints);
  }
  
  private getBasePoints(actionType: ActionType): number {
    const pointsMap = {
      [ActionType.WORD_LEARNED]: 10,
      [ActionType.EXERCISE_COMPLETED]: 25,
      [ActionType.STREAK_MAINTAINED]: 5,
      [ActionType.PERFECT_SCORE]: 50,
      [ActionType.STUDY_SESSION]: 20,
      [ActionType.SOCIAL_INTERACTION]: 15,
      [ActionType.CONTENT_CREATED]: 100
    };
    
    return pointsMap[actionType] || 0;
  }
  
  private getMultipliers(action: GameAction, profile: UserProfile): Multiplier[] {
    const multipliers: Multiplier[] = [];
    
    // Streak multiplier
    if (profile.current_streak > 0) {
      const streakMultiplier = Math.min(1 + (profile.current_streak * 0.1), 3.0);
      multipliers.push({ type: 'streak', value: streakMultiplier });
    }
    
    // Difficulty multiplier
    if (action.data.difficulty) {
      const difficultyMultipliers = {
        'beginner': 1.0,
        'intermediate': 1.5,
        'advanced': 2.0
      };
      multipliers.push({ 
        type: 'difficulty', 
        value: difficultyMultipliers[action.data.difficulty] || 1.0 
      });
    }
    
    return multipliers;
  }
}
```

#### 1.2 Badge System
```typescript
class BadgeManager {
  async checkBadgeEligibility(
    userId: string,
    action: GameAction,
    userStats: UserStats
  ): Promise<Badge[]> {
    const eligibleBadges: Badge[] = [];
    const allBadges = await this.getAllActiveBadges();
    const userBadges = await this.getUserBadges(userId);
    const earnedBadgeIds = new Set(userBadges.map(ub => ub.badge_id));
    
    for (const badge of allBadges) {
      if (!earnedBadgeIds.has(badge.id)) {
        const isEligible = await this.evaluateBadgeRequirements(
          badge,
          action,
          userStats
        );
        
        if (isEligible) {
          eligibleBadges.push(badge);
        }
      }
    }
    
    return eligibleBadges;
  }
  
  private async evaluateBadgeRequirements(
    badge: Badge,
    action: GameAction,
    userStats: UserStats
  ): Promise<boolean> {
    const requirements = badge.requirements as BadgeRequirements;
    
    // Check action-based requirements
    if (requirements.action_type && requirements.action_type !== action.type) {
      return false;
    }
    
    // Check statistical requirements
    for (const [stat, requirement] of Object.entries(requirements.stats || {})) {
      const userValue = userStats[stat as keyof UserStats];
      
      if (!this.meetsRequirement(userValue, requirement)) {
        return false;
      }
    }
    
    // Check streak requirements
    if (requirements.streak && userStats.current_streak < requirements.streak) {
      return false;
    }
    
    // Check accuracy requirements
    if (requirements.accuracy && userStats.overall_accuracy < requirements.accuracy) {
      return false;
    }
    
    return true;
  }
  
  async awardBadge(userId: string, badge: Badge): Promise<UserBadge> {
    const userBadge = await this.createUserBadge(userId, badge.id);
    
    // Award points
    if (badge.points_value > 0) {
      await this.awardPoints(userId, badge.points_value, `Badge: ${badge.name}`);
    }
    
    // Send notification
    await this.sendBadgeNotification(userId, badge);
    
    return userBadge;
  }
}
```

### Phase 2: Social Features (Week 3-4)

#### 2.1 Study Groups
```typescript
// src/backend/services/social.service.ts
export interface SocialService {
  createStudyGroup(creatorId: string, groupData: CreateGroupData): Promise<StudyGroup>;
  joinStudyGroup(userId: string, groupCode: string): Promise<StudyGroupMember>;
  getGroupLeaderboard(groupId: string): Promise<LeaderboardEntry[]>;
  createGroupChallenge(groupId: string, challengeData: CreateChallengeData): Promise<Challenge>;
  shareContent(userId: string, contentData: ShareContentData): Promise<CommunityContent>;
}

class StudyGroupManager {
  async createStudyGroup(
    creatorId: string,
    groupData: CreateGroupData
  ): Promise<StudyGroup> {
    const groupCode = this.generateGroupCode();
    
    const group = await this.prisma.studyGroup.create({
      data: {
        name: groupData.name,
        description: groupData.description,
        creator_id: creatorId,
        is_public: groupData.isPublic,
        max_members: groupData.maxMembers,
        group_code: groupCode,
        group_image_url: groupData.imageUrl
      }
    });
    
    // Add creator as admin member
    await this.addGroupMember(group.id, creatorId, 'admin');
    
    // Award points for creating group
    await this.gamificationService.awardPoints(
      creatorId,
      100,
      'Created study group'
    );
    
    return group;
  }
  
  async facilitateGroupChallenge(
    groupId: string,
    challengeData: CreateChallengeData
  ): Promise<Challenge> {
    const challenge = await this.prisma.challenge.create({
      data: {
        title: challengeData.title,
        description: challengeData.description,
        type: 'group',
        category: challengeData.category,
        difficulty: challengeData.difficulty,
        duration: challengeData.duration,
        target_metric: challengeData.targetMetric,
        target_value: challengeData.targetValue,
        reward_points: challengeData.rewardPoints,
        start_date: challengeData.startDate,
        end_date: challengeData.endDate,
        created_by: challengeData.createdBy,
        max_participants: challengeData.maxParticipants
      }
    });
    
    // Auto-enroll group members
    const groupMembers = await this.getGroupMembers(groupId);
    for (const member of groupMembers) {
      await this.enrollInChallenge(challenge.id, member.user_id);
    }
    
    return challenge;
  }
}
```

#### 2.2 Peer Challenges
```typescript
class PeerChallengeSystem {
  async createPeerChallenge(
    challengerId: string,
    challengedId: string,
    challengeData: PeerChallengeData
  ): Promise<Challenge> {
    const challenge = await this.prisma.challenge.create({
      data: {
        title: `${challengeData.title} - Peer Challenge`,
        description: challengeData.description,
        type: 'peer_vs_peer',
        category: challengeData.category,
        difficulty: challengeData.difficulty,
        duration: challengeData.duration,
        target_metric: challengeData.metric,
        target_value: challengeData.targetValue,
        start_date: new Date(),
        end_date: new Date(Date.now() + challengeData.duration * 24 * 60 * 60 * 1000),
        created_by: challengerId,
        max_participants: 2
      }
    });
    
    // Enroll both participants
    await Promise.all([
      this.enrollInChallenge(challenge.id, challengerId),
      this.enrollInChallenge(challenge.id, challengedId)
    ]);
    
    // Send notification to challenged user
    await this.sendChallengeNotification(challengedId, challenge, challengerId);
    
    return challenge;
  }
  
  async updateChallengeProgress(
    challengeId: string,
    userId: string,
    progress: number
  ): Promise<void> {
    await this.prisma.challengeParticipant.update({
      where: {
        challenge_id_user_id: {
          challenge_id: challengeId,
          user_id: userId
        }
      },
      data: {
        current_progress: progress
      }
    });
    
    // Check if challenge is completed
    await this.checkChallengeCompletion(challengeId);
  }
  
  private async checkChallengeCompletion(challengeId: string): Promise<void> {
    const challenge = await this.getChallenge(challengeId);
    const participants = await this.getChallengeParticipants(challengeId);
    
    // Check if any participant reached the target
    const completedParticipants = participants.filter(
      p => p.current_progress >= challenge.target_value
    );
    
    if (completedParticipants.length > 0) {
      await this.completePeerChallenge(challenge, participants);
    }
  }
}
```

### Phase 3: Community Content System (Week 5-6)

#### 3.1 Content Creation and Sharing
```typescript
class CommunityContentManager {
  async createContent(
    creatorId: string,
    contentData: CreateContentData
  ): Promise<CommunityContent> {
    const content = await this.prisma.communityContent.create({
      data: {
        creator_id: creatorId,
        content_type: contentData.type,
        title: contentData.title,
        description: contentData.description,
        content_data: contentData.data,
        difficulty: contentData.difficulty,
        language: contentData.language,
        tags: contentData.tags
      }
    });
    
    // Award points for content creation
    await this.gamificationService.awardPoints(
      creatorId,
      this.getContentCreationPoints(contentData.type),
      `Created ${contentData.type}: ${contentData.title}`
    );
    
    // Check for content creation badges
    await this.checkContentCreationBadges(creatorId);
    
    return content;
  }
  
  async rateContent(
    userId: string,
    contentId: string,
    rating: number
  ): Promise<void> {
    // Create or update rating
    await this.prisma.contentInteraction.upsert({
      where: {
        user_id_content_id_interaction_type: {
          user_id: userId,
          content_id: contentId,
          interaction_type: 'rating'
        }
      },
      update: {
        value: rating
      },
      create: {
        user_id: userId,
        content_id: contentId,
        interaction_type: 'rating',
        value: rating
      }
    });
    
    // Update content average rating
    await this.updateContentRating(contentId);
    
    // Award points to content creator if rating is high
    if (rating >= 4) {
      const content = await this.getContent(contentId);
      await this.gamificationService.awardPoints(
        content.creator_id,
        5,
        'Received high rating on content'
      );
    }
  }
  
  async moderateContent(
    moderatorId: string,
    contentId: string,
    action: ModerationAction
  ): Promise<void> {
    switch (action.type) {
      case 'approve':
        await this.approveContent(contentId);
        break;
      case 'reject':
        await this.rejectContent(contentId, action.reason);
        break;
      case 'feature':
        await this.featureContent(contentId);
        break;
    }
    
    // Log moderation action
    await this.logModerationAction(moderatorId, contentId, action);
  }
}
```

### Phase 4: Leaderboards and Competition (Week 7-8)

#### 4.1 Dynamic Leaderboards
```typescript
class LeaderboardManager {
  async updateLeaderboards(): Promise<void> {
    const leaderboards = await this.getAllActiveLeaderboards();
    
    for (const leaderboard of leaderboards) {
      await this.updateLeaderboard(leaderboard);
    }
  }
  
  private async updateLeaderboard(leaderboard: Leaderboard): Promise<void> {
    const scores = await this.calculateScores(leaderboard);
    const rankedScores = this.rankScores(scores);
    
    // Update leaderboard entries
    for (const [index, score] of rankedScores.entries()) {
      const rank = index + 1;
      const previousEntry = await this.getPreviousEntry(leaderboard.id, score.userId);
      const rankChange = previousEntry ? previousEntry.rank - rank : 0;
      
      await this.prisma.leaderboardEntry.upsert({
        where: {
          leaderboard_id_user_id: {
            leaderboard_id: leaderboard.id,
            user_id: score.userId
          }
        },
        update: {
          rank,
          score: score.value,
          previous_rank: previousEntry?.rank,
          rank_change: rankChange,
          calculated_at: new Date()
        },
        create: {
          leaderboard_id: leaderboard.id,
          user_id: score.userId,
          rank,
          score: score.value,
          rank_change: 0
        }
      });
    }
    
    // Send rank change notifications
    await this.sendRankChangeNotifications(leaderboard.id, rankedScores);
  }
  
  private async calculateScores(leaderboard: Leaderboard): Promise<UserScore[]> {
    const timeFilter = this.getTimeFilter(leaderboard.time_period);
    
    switch (leaderboard.metric) {
      case 'points':
        return await this.calculatePointsScores(timeFilter);
      case 'streak':
        return await this.calculateStreakScores();
      case 'words_learned':
        return await this.calculateWordsLearnedScores(timeFilter);
      case 'accuracy':
        return await this.calculateAccuracyScores(timeFilter);
      default:
        return [];
    }
  }
}
```

## Frontend Integration

### Gamification Dashboard
```typescript
// src/components/ui/gamification-dashboard.tsx
export function GamificationDashboard({ userId }: { userId: string }) {
  const { profile, badges, leaderboards } = useGamification(userId);
  const { studyGroups } = useStudyGroups(userId);
  const { challenges } = useChallenges(userId);
  
  return (
    <div className="gamification-dashboard">
      <div className="profile-section">
        <UserProfileCard profile={profile} />
        <StreakIndicator streak={profile.current_streak} />
        <LevelProgressBar 
          level={profile.level} 
          experience={profile.experience_points} 
        />
      </div>
      
      <div className="achievements-section">
        <h3>Recent Badges</h3>
        <BadgeGallery badges={badges.slice(0, 6)} />
      </div>
      
      <div className="social-section">
        <StudyGroupsList groups={studyGroups} />
        <ActiveChallenges challenges={challenges} />
      </div>
      
      <div className="leaderboards-section">
        <LeaderboardTabs leaderboards={leaderboards} />
      </div>
    </div>
  );
}
```

### Custom Hooks
```typescript
// src/hooks/use-gamification.ts
export function useGamification(userId: string) {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [badges, setBadges] = useState<UserBadge[]>([]);
  const [leaderboards, setLeaderboards] = useState<LeaderboardEntry[]>([]);
  
  const awardPoints = useCallback(async (points: number, reason: string) => {
    const result = await awardPointsApi(userId, points, reason);
    setProfile(prev => prev ? { ...prev, total_points: result.newTotal } : null);
    return result;
  }, [userId]);
  
  const checkBadges = useCallback(async (action: GameAction) => {
    const newBadges = await checkBadgeEligibilityApi(userId, action);
    if (newBadges.length > 0) {
      setBadges(prev => [...newBadges, ...prev]);
      // Show badge notification
      newBadges.forEach(badge => showBadgeNotification(badge));
    }
  }, [userId]);
  
  return {
    profile,
    badges,
    leaderboards,
    awardPoints,
    checkBadges
  };
}
```

## Success Criteria

### Engagement Metrics
- 50% increase in daily active users
- 40% increase in session duration
- 60% increase in user retention (7-day)
- 35% increase in feature adoption

### Social Metrics
- 25% of users join study groups
- 15% of users create community content
- 30% of users participate in challenges
- 20% improvement in peer interaction

### Learning Metrics
- 30% improvement in learning consistency
- 25% increase in goal completion rates
- 20% improvement in long-term retention
- 40% increase in study streak maintenance

## Timeline

- **Week 1-2**: Core gamification engine and points system
- **Week 3-4**: Social features (study groups, peer challenges)
- **Week 5-6**: Community content system and moderation
- **Week 7-8**: Leaderboards and advanced competition features
- **Week 9**: Testing and balancing
- **Week 10**: Deployment and community launch
