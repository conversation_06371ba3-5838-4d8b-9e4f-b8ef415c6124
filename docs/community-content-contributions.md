# Community Content Contributions Development Plan

## Overview
Implement a comprehensive user-generated content system that allows community members to contribute vocabulary words, definitions, examples, paragraphs, and exercises while maintaining quality through moderation and peer review.

## Technical Architecture

### Database Schema Extensions

#### Content Contribution Models
```prisma
model ContentContribution {
  id              String              @id @default(uuid())
  type            ContributionType
  title           String
  description     String?
  content         Json                // Flexible content structure
  language        Language            @default(EN)
  difficulty      Difficulty          @default(BEGINNER)
  tags            String[]
  status          ContributionStatus  @default(PENDING)
  submittedBy     String
  reviewedBy      String?
  reviewedAt      DateTime?
  reviewNotes     String?
  upvotes         Int                 @default(0)
  downvotes       Int                 @default(0)
  usageCount      Int                 @default(0)
  isOfficial      Boolean             @default(false)
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  
  submitter       User                @relation("SubmittedContributions", fields: [submittedBy], references: [id])
  reviewer        User?               @relation("ReviewedContributions", fields: [reviewedBy], references: [id])
  votes           ContributionVote[]
  reports         ContributionReport[]
  comments        ContributionComment[]
  
  @@index([type, status])
  @@index([submittedBy])
  @@index([language, difficulty])
  @@map("content_contributions")
}

model ContributionVote {
  id              String              @id @default(uuid())
  contributionId  String
  userId          String
  voteType        VoteType
  createdAt       DateTime            @default(now())
  
  contribution    ContentContribution @relation(fields: [contributionId], references: [id], onDelete: Cascade)
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([contributionId, userId])
  @@index([contributionId])
  @@map("contribution_votes")
}

model ContributionReport {
  id              String              @id @default(uuid())
  contributionId  String
  reportedBy      String
  reason          ReportReason
  description     String?
  status          ReportStatus        @default(PENDING)
  resolvedBy      String?
  resolvedAt      DateTime?
  resolution      String?
  createdAt       DateTime            @default(now())
  
  contribution    ContentContribution @relation(fields: [contributionId], references: [id], onDelete: Cascade)
  reporter        User                @relation("SubmittedReports", fields: [reportedBy], references: [id])
  resolver        User?               @relation("ResolvedReports", fields: [resolvedBy], references: [id])
  
  @@index([contributionId])
  @@index([status])
  @@map("contribution_reports")
}

model ContributionComment {
  id              String              @id @default(uuid())
  contributionId  String
  userId          String
  content         String
  parentId        String?             // For nested comments
  isReviewComment Boolean             @default(false)
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  
  contribution    ContentContribution @relation(fields: [contributionId], references: [id], onDelete: Cascade)
  user            User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  parent          ContributionComment? @relation("CommentReplies", fields: [parentId], references: [id])
  replies         ContributionComment[] @relation("CommentReplies")
  
  @@index([contributionId])
  @@index([userId])
  @@map("contribution_comments")
}

model ContributorStats {
  id                    String   @id @default(uuid())
  userId                String   @unique
  totalContributions    Int      @default(0)
  approvedContributions Int      @default(0)
  rejectedContributions Int      @default(0)
  totalUpvotes          Int      @default(0)
  totalDownvotes        Int      @default(0)
  contributorLevel      Int      @default(1)
  contributorPoints     Int      @default(0)
  specializations       String[] // Areas of expertise
  lastContributionAt    DateTime?
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([contributorLevel])
  @@index([contributorPoints])
  @@map("contributor_stats")
}

enum ContributionType {
  WORD_DEFINITION
  WORD_EXAMPLE
  PARAGRAPH
  EXERCISE
  AUDIO_PRONUNCIATION
  IMAGE_ASSOCIATION
  CULTURAL_NOTE
  GRAMMAR_TIP
}

enum ContributionStatus {
  PENDING
  APPROVED
  REJECTED
  NEEDS_REVISION
  ARCHIVED
}

enum VoteType {
  UPVOTE
  DOWNVOTE
}

enum ReportReason {
  INAPPROPRIATE_CONTENT
  INCORRECT_INFORMATION
  SPAM
  COPYRIGHT_VIOLATION
  DUPLICATE_CONTENT
  OTHER
}

enum ReportStatus {
  PENDING
  REVIEWED
  RESOLVED
  DISMISSED
}
```

#### User Model Extensions
```prisma
model User {
  // ... existing fields
  submittedContributions  ContentContribution[] @relation("SubmittedContributions")
  reviewedContributions   ContentContribution[] @relation("ReviewedContributions")
  contributionVotes       ContributionVote[]
  submittedReports        ContributionReport[]  @relation("SubmittedReports")
  resolvedReports         ContributionReport[]  @relation("ResolvedReports")
  contributionComments    ContributionComment[]
  contributorStats        ContributorStats?
  isModerator             Boolean               @default(false)
  isContributor           Boolean               @default(false)
}
```

### Backend Implementation

#### Services

**Content Contribution Service** (`src/backend/services/content-contribution.service.ts`)
```typescript
export interface ContentContributionService {
  submitContribution(userId: string, contributionData: SubmitContributionDto): Promise<ContentContribution>;
  getContributions(filters: ContributionFilters): Promise<ContentContribution[]>;
  getUserContributions(userId: string, status?: ContributionStatus): Promise<ContentContribution[]>;
  reviewContribution(reviewerId: string, contributionId: string, review: ReviewDto): Promise<ContentContribution>;
  voteOnContribution(userId: string, contributionId: string, voteType: VoteType): Promise<ContributionVote>;
  reportContribution(userId: string, contributionId: string, report: ReportDto): Promise<ContributionReport>;
  getContributorStats(userId: string): Promise<ContributorStats>;
  promoteToContributor(userId: string): Promise<User>;
}

export class ContentContributionServiceImpl implements ContentContributionService {
  constructor(
    private getContentContributionRepository: () => ContentContributionRepository,
    private getContributionVoteRepository: () => ContributionVoteRepository,
    private getContributorStatsRepository: () => ContributorStatsRepository,
    private getNotificationService: () => NotificationService,
    private getModerationService: () => ModerationService
  ) {}

  async submitContribution(
    userId: string, 
    contributionData: SubmitContributionDto
  ): Promise<ContentContribution> {
    // Validate content based on type
    await this.validateContributionContent(contributionData);

    const contribution = await this.getContentContributionRepository().create({
      ...contributionData,
      submittedBy: userId,
      status: ContributionStatus.PENDING,
    });

    // Update contributor stats
    await this.updateContributorStats(userId, 'submitted');

    // Auto-moderate content
    const moderationResult = await this.getModerationService()
      .moderateContent(contribution.content);
    
    if (moderationResult.requiresReview) {
      await this.notifyModerators(contribution);
    } else if (moderationResult.autoApprove) {
      await this.autoApproveContribution(contribution.id);
    }

    return contribution;
  }

  async reviewContribution(
    reviewerId: string, 
    contributionId: string, 
    review: ReviewDto
  ): Promise<ContentContribution> {
    const contribution = await this.getContentContributionRepository()
      .findById(contributionId);
    
    if (!contribution) {
      throw new NotFoundError('Contribution not found');
    }

    if (contribution.status !== ContributionStatus.PENDING) {
      throw new ValidationError('Contribution has already been reviewed');
    }

    const updatedContribution = await this.getContentContributionRepository()
      .update(contributionId, {
        status: review.status,
        reviewedBy: reviewerId,
        reviewedAt: new Date(),
        reviewNotes: review.notes,
      });

    // Update contributor stats
    await this.updateContributorStats(
      contribution.submittedBy, 
      review.status === ContributionStatus.APPROVED ? 'approved' : 'rejected'
    );

    // Notify contributor
    await this.getNotificationService().sendNotification(contribution.submittedBy, {
      type: 'contribution_reviewed',
      title: 'Contribution Reviewed',
      message: `Your contribution "${contribution.title}" has been ${review.status.toLowerCase()}`,
      data: { contributionId, status: review.status },
    });

    // If approved, integrate into main content
    if (review.status === ContributionStatus.APPROVED) {
      await this.integrateApprovedContent(updatedContribution);
    }

    return updatedContribution;
  }

  async voteOnContribution(
    userId: string, 
    contributionId: string, 
    voteType: VoteType
  ): Promise<ContributionVote> {
    const contribution = await this.getContentContributionRepository()
      .findById(contributionId);
    
    if (!contribution) {
      throw new NotFoundError('Contribution not found');
    }

    if (contribution.submittedBy === userId) {
      throw new ValidationError('Cannot vote on your own contribution');
    }

    // Check for existing vote
    const existingVote = await this.getContributionVoteRepository()
      .findByContributionAndUser(contributionId, userId);

    if (existingVote) {
      if (existingVote.voteType === voteType) {
        // Remove vote if same type
        await this.getContributionVoteRepository().delete(existingVote.id);
        await this.updateVoteCounts(contributionId, voteType, -1);
        return existingVote;
      } else {
        // Update vote type
        const updatedVote = await this.getContributionVoteRepository()
          .update(existingVote.id, { voteType });
        await this.updateVoteCounts(contributionId, existingVote.voteType, -1);
        await this.updateVoteCounts(contributionId, voteType, 1);
        return updatedVote;
      }
    }

    // Create new vote
    const vote = await this.getContributionVoteRepository().create({
      contributionId,
      userId,
      voteType,
    });

    await this.updateVoteCounts(contributionId, voteType, 1);
    
    // Update contributor stats for the content creator
    await this.updateContributorStats(
      contribution.submittedBy, 
      voteType === VoteType.UPVOTE ? 'upvoted' : 'downvoted'
    );

    return vote;
  }

  private async validateContributionContent(data: SubmitContributionDto): Promise<void> {
    switch (data.type) {
      case ContributionType.WORD_DEFINITION:
        if (!data.content.word || !data.content.definition) {
          throw new ValidationError('Word and definition are required');
        }
        break;
      case ContributionType.PARAGRAPH:
        if (!data.content.text || data.content.text.length < 50) {
          throw new ValidationError('Paragraph must be at least 50 characters long');
        }
        break;
      case ContributionType.EXERCISE:
        if (!data.content.question || !data.content.options || !data.content.correctAnswer) {
          throw new ValidationError('Exercise must have question, options, and correct answer');
        }
        break;
    }
  }

  private async integrateApprovedContent(contribution: ContentContribution): Promise<void> {
    switch (contribution.type) {
      case ContributionType.WORD_DEFINITION:
        await this.integrateWordDefinition(contribution);
        break;
      case ContributionType.PARAGRAPH:
        await this.integrateParagraph(contribution);
        break;
      case ContributionType.EXERCISE:
        await this.integrateExercise(contribution);
        break;
    }
  }

  private async updateContributorStats(
    userId: string, 
    action: 'submitted' | 'approved' | 'rejected' | 'upvoted' | 'downvoted'
  ): Promise<void> {
    const stats = await this.getContributorStatsRepository().findByUserId(userId);
    
    const updates: Partial<ContributorStats> = {};
    
    switch (action) {
      case 'submitted':
        updates.totalContributions = stats.totalContributions + 1;
        updates.lastContributionAt = new Date();
        break;
      case 'approved':
        updates.approvedContributions = stats.approvedContributions + 1;
        updates.contributorPoints = stats.contributorPoints + 10;
        break;
      case 'rejected':
        updates.rejectedContributions = stats.rejectedContributions + 1;
        break;
      case 'upvoted':
        updates.totalUpvotes = stats.totalUpvotes + 1;
        updates.contributorPoints = stats.contributorPoints + 1;
        break;
      case 'downvoted':
        updates.totalDownvotes = stats.totalDownvotes + 1;
        break;
    }

    // Calculate new level
    if (updates.contributorPoints) {
      updates.contributorLevel = Math.floor(Math.sqrt(updates.contributorPoints / 100)) + 1;
    }

    await this.getContributorStatsRepository().update(userId, updates);
  }
}
```

### Frontend Implementation

#### Components

**Contribution Form Component** (`src/components/ui/contribution-form.tsx`)
```typescript
interface ContributionFormProps {
  type: ContributionType;
  onSubmit: (data: SubmitContributionDto) => void;
  onCancel: () => void;
}

export function ContributionForm({ type, onSubmit, onCancel }: ContributionFormProps) {
  const [formData, setFormData] = useState<Partial<SubmitContributionDto>>({
    type,
    language: Language.EN,
    difficulty: Difficulty.BEGINNER,
    tags: [],
  });

  const renderContentFields = () => {
    switch (type) {
      case ContributionType.WORD_DEFINITION:
        return (
          <>
            <div>
              <Label htmlFor="word">Word</Label>
              <Input
                id="word"
                value={formData.content?.word || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  content: { ...prev.content, word: e.target.value }
                }))}
                placeholder="Enter the word"
                required
              />
            </div>
            <div>
              <Label htmlFor="definition">Definition</Label>
              <Textarea
                id="definition"
                value={formData.content?.definition || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  content: { ...prev.content, definition: e.target.value }
                }))}
                placeholder="Enter the definition"
                rows={3}
                required
              />
            </div>
            <div>
              <Label htmlFor="example">Example (Optional)</Label>
              <Textarea
                id="example"
                value={formData.content?.example || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  content: { ...prev.content, example: e.target.value }
                }))}
                placeholder="Enter an example sentence"
                rows={2}
              />
            </div>
          </>
        );
      
      case ContributionType.PARAGRAPH:
        return (
          <div>
            <Label htmlFor="text">Paragraph Text</Label>
            <Textarea
              id="text"
              value={formData.content?.text || ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                content: { ...prev.content, text: e.target.value }
              }))}
              placeholder="Enter the paragraph (minimum 50 characters)"
              rows={6}
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              {formData.content?.text?.length || 0} characters
            </p>
          </div>
        );
      
      case ContributionType.EXERCISE:
        return (
          <>
            <div>
              <Label htmlFor="question">Question</Label>
              <Textarea
                id="question"
                value={formData.content?.question || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  content: { ...prev.content, question: e.target.value }
                }))}
                placeholder="Enter the exercise question"
                rows={2}
                required
              />
            </div>
            <div>
              <Label>Answer Options</Label>
              {[0, 1, 2, 3].map(index => (
                <Input
                  key={index}
                  value={formData.content?.options?.[index] || ''}
                  onChange={(e) => {
                    const options = [...(formData.content?.options || ['', '', '', ''])];
                    options[index] = e.target.value;
                    setFormData(prev => ({
                      ...prev,
                      content: { ...prev.content, options }
                    }));
                  }}
                  placeholder={`Option ${index + 1}`}
                  className="mt-1"
                  required
                />
              ))}
            </div>
            <div>
              <Label htmlFor="correctAnswer">Correct Answer (0-3)</Label>
              <Select
                value={formData.content?.correctAnswer?.toString() || ''}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  content: { ...prev.content, correctAnswer: parseInt(value) }
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select correct answer" />
                </SelectTrigger>
                <SelectContent>
                  {[0, 1, 2, 3].map(index => (
                    <SelectItem key={index} value={index.toString()}>
                      Option {index + 1}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </>
        );
      
      default:
        return null;
    }
  };

  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      onSubmit(formData as SubmitContributionDto);
    }} className="space-y-4">
      <div>
        <Label htmlFor="title">Title</Label>
        <Input
          id="title"
          value={formData.title || ''}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          placeholder="Enter a descriptive title"
          required
        />
      </div>

      <div>
        <Label htmlFor="description">Description (Optional)</Label>
        <Textarea
          id="description"
          value={formData.description || ''}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Provide additional context or notes"
          rows={2}
        />
      </div>

      {renderContentFields()}

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="language">Language</Label>
          <Select
            value={formData.language}
            onValueChange={(value) => setFormData(prev => ({ ...prev, language: value as Language }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={Language.EN}>English</SelectItem>
              <SelectItem value={Language.VI}>Vietnamese</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="difficulty">Difficulty</Label>
          <Select
            value={formData.difficulty}
            onValueChange={(value) => setFormData(prev => ({ ...prev, difficulty: value as Difficulty }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={Difficulty.BEGINNER}>Beginner</SelectItem>
              <SelectItem value={Difficulty.INTERMEDIATE}>Intermediate</SelectItem>
              <SelectItem value={Difficulty.ADVANCED}>Advanced</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          Submit Contribution
        </Button>
      </div>
    </form>
  );
}
```

## Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure
- Database schema implementation
- Basic contribution service and repository
- Content submission system

### Phase 2 (Weeks 3-4): Review & Moderation
- Review workflow implementation
- Moderation tools and automation
- Voting and reporting system

### Phase 3 (Weeks 5-6): Frontend Components
- Contribution forms and interfaces
- Review dashboard for moderators
- Community contribution gallery

### Phase 4 (Weeks 7-8): Integration & Gamification
- Content integration into main system
- Contributor recognition and rewards
- Advanced moderation features

## Success Metrics
- Number of quality contributions submitted
- Community engagement and participation
- Content approval rates
- User satisfaction with contributed content
- Reduction in content creation workload

## Future Enhancements
- AI-assisted content validation
- Collaborative editing features
- Expert contributor program
- Content licensing and attribution
- Multi-language content translation
