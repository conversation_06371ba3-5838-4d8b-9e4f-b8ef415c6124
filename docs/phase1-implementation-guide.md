# Phase 1 Implementation Guide - Token Optimization

## Tổng Quan

Phase 1 đã triển khai các optimizations cơ bản để giảm chi phí OpenAI API:

### ✅ Đã Triển Khai

1. **PromptOptimizerService** - T<PERSON><PERSON> <PERSON><PERSON> prompts để giảm 30-50% tokens
2. **Enhanced CacheService** - Caching thông minh với TTL optimization
3. **TokenMonitorService** - <PERSON> dõi và phân tích token usage
4. **Configuration Updates** - Thêm các settings cho optimization
5. **API Monitoring** - Endpoints để monitor token usage

## Cách Sử Dụng

### 1. Cấu Hình Environment Variables

Thêm vào file `.env`:

```bash
# LLM Optimization Settings
LLM_OPTIMIZATION_ENABLED=true
LLM_PROMPT_OPTIMIZATION_ENABLED=true
LLM_CACHING_ENABLED=true

# Cache TTL Settings (seconds)
LLM_CACHE_TTL_VOCABULARY=604800      # 7 days
LLM_CACHE_TTL_WORD_DETAILS=604800    # 7 days  
LLM_CACHE_TTL_PARAGRAPHS=259200      # 3 days
LLM_CACHE_TTL_EVALUATIONS=2592000    # 30 days

# Token Budget Management
LLM_TOKEN_BUDGET_DAILY=100000        # Daily token limit
LLM_COST_ALERT_DAILY=10.0           # Daily cost alert ($)

# Monitoring
LLM_MONITORING_ENABLED=true
LLM_LOG_LEVEL=info
```

### 2. Sử Dụng PromptOptimizerService

```typescript
import { PromptOptimizerService } from '@/backend/services/prompt-optimizer.service';

// Optimize a prompt
const result = PromptOptimizerService.optimizePrompt('vocabulary', {
  count: 10,
  target_lang: 'Vietnamese',
  source_lang: 'English',
  keywords: ['technology', 'computer'],
  excludes: ['old', 'obsolete']
});

console.log(`Token savings: ${result.tokenSavings}%`);
console.log(`Optimized prompt: ${result.optimizedPrompt}`);
```

### 3. Enhanced Caching

```typescript
import { CacheService } from '@/backend/services/cache.service';

const cache = new CacheService();

// Set with optimized options
cache.setOptimized('key', data, {
  ttl: CacheService.getOptimizedTTL('vocabulary'),
  tags: ['vocabulary', 'user123'],
  priority: 'high'
});

// Get with tracking
const result = cache.getOptimized('key');
```

### 4. Token Monitoring

```typescript
import { tokenMonitor } from '@/backend/services/token-monitor.service';

// Track usage (automatically done in LLMService)
tokenMonitor.trackUsage({
  endpoint: 'generateVocabulary',
  operation: 'vocabulary',
  inputTokens: 500,
  outputTokens: 200,
  model: 'gpt-4o-mini',
  optimized: true,
  compressionRatio: 0.6
});

// Get statistics
const stats = tokenMonitor.getStats('day');
console.log(`Daily cost: $${stats.totalCost.toFixed(4)}`);
console.log(`Token savings: ${stats.optimizationSavings.tokensSaved}`);
```

## API Endpoints

### Token Monitoring APIs

```bash
# Get token usage stats
GET /api/token-monitor/stats?timeframe=day

# Get cost analysis
GET /api/token-monitor/analysis

# Get optimization suggestions
GET /api/token-monitor/suggestions

# Check budget alerts
GET /api/token-monitor/alerts

# Get cache performance
GET /api/token-monitor/cache-stats
```

### Example Response

```json
{
  "success": true,
  "data": {
    "totalTokens": 15000,
    "totalCost": 0.045,
    "requestCount": 25,
    "averageTokensPerRequest": 600,
    "optimizationSavings": {
      "tokensSaved": 6000,
      "costSaved": 0.018,
      "compressionRatio": 0.6
    }
  }
}
```

## Tích Hợp Vào LLMService

LLMService đã được cập nhật để tự động sử dụng optimizations:

```typescript
// Trong LLMService, các methods sẽ tự động:
// 1. Check cache trước khi gọi API
// 2. Optimize prompts nếu enabled
// 3. Track token usage
// 4. Cache kết quả với TTL phù hợp

// Ví dụ sử dụng:
const llmService = new LLMService(getWordService);
const words = await llmService.generateRandomTerms({
  // ... params
});
// Tự động optimized và cached
```

## Monitoring Dashboard

### Metrics Quan Trọng

1. **Token Usage Trends**
   - Daily/weekly/monthly consumption
   - Cost per operation
   - Optimization savings

2. **Cache Performance**
   - Hit rate percentage
   - Memory usage
   - TTL effectiveness

3. **Cost Analysis**
   - Cost breakdown by operation
   - Budget utilization
   - Trend analysis

### Alerts

Hệ thống sẽ tự động cảnh báo khi:
- Daily token usage > 80% budget
- Daily cost > threshold
- Cache hit rate < 50%
- Optimization savings < 20%

## Best Practices

### 1. Cache Strategy

```typescript
// High priority cho stable content
cache.setOptimized(key, data, {
  ttl: CacheService.getOptimizedTTL('wordDetails'),
  priority: 'high',
  tags: ['stable', 'vocabulary']
});

// Low priority cho dynamic content
cache.setOptimized(key, data, {
  ttl: CacheService.getOptimizedTTL('grammarPractice'),
  priority: 'low',
  tags: ['dynamic', 'practice']
});
```

### 2. Prompt Optimization

```typescript
// Validate parameters trước khi optimize
const errors = PromptOptimizerService.validateParameters('vocabulary', params);
if (errors.length > 0) {
  console.warn('Validation errors:', errors);
}

// Fallback cho custom prompts
const customResult = PromptOptimizerService.createCustomOptimizedPrompt(originalPrompt);
```

### 3. Monitoring

```typescript
// Regular health checks
setInterval(() => {
  const alerts = tokenMonitor.checkBudgetAlerts();
  if (alerts.length > 0) {
    console.warn('Budget alerts:', alerts);
    // Send notifications
  }
}, 60000); // Check every minute
```

## Kết Quả Kỳ Vọng

### Immediate Benefits (1-2 tuần)

- ✅ **30-40% reduction** trong token usage
- ✅ **50-70% cache hit rate** cho common requests  
- ✅ **Real-time monitoring** của token consumption
- ✅ **Automated alerts** cho budget thresholds

### Performance Improvements

- **Response Time**: Giảm 40-60% nhờ caching
- **API Calls**: Giảm 50-70% cho repeated requests
- **Cost Predictability**: Better budget management
- **Quality Maintenance**: Không giảm chất lượng output

## Troubleshooting

### Common Issues

1. **Cache Miss Rate Cao**
   ```typescript
   // Check cache configuration
   const stats = cache.getStats();
   console.log('Hit rate:', stats.hitRate);
   
   // Adjust TTL if needed
   const ttl = CacheService.getOptimizedTTL('operation');
   ```

2. **Prompt Optimization Failures**
   ```typescript
   // Enable fallback
   try {
     const optimized = PromptOptimizerService.optimizePrompt(template, params);
   } catch (error) {
     console.warn('Using original prompt:', error);
     // Use original prompt
   }
   ```

3. **Token Budget Exceeded**
   ```typescript
   // Check current usage
   const alerts = tokenMonitor.checkBudgetAlerts();
   
   // Implement rate limiting if needed
   if (alerts.some(a => a.severity === 'critical')) {
     // Pause non-essential operations
   }
   ```

## Next Steps

Phase 1 đã hoàn thành foundation. Tiếp theo:

1. **Phase 2**: Batch processing và semantic caching
2. **Phase 3**: Custom model fine-tuning
3. **Advanced Analytics**: ML-based optimization suggestions
4. **User Dashboard**: Real-time cost monitoring UI

## Support

Nếu gặp vấn đề:
1. Check logs với `LLM_LOG_LEVEL=debug`
2. Monitor token usage qua API endpoints
3. Review optimization suggestions
4. Adjust configuration based on usage patterns
