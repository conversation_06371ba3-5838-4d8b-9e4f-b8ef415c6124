# Grade Book Integration Development Plan

## Overview
Implement comprehensive grade book integration that seamlessly connects with popular Learning Management Systems (LMS) and provides robust internal grade tracking, analytics, and reporting capabilities for educators and institutions.

## Technical Architecture

### Database Schema Extensions
```prisma
model GradeBook {
  id          String   @id @default(uuid())
  name        String
  teacher_id  String
  course_code String?
  semester    String?
  year        Int?
  is_active   Boolean  @default(true)
  settings    Json     // Grading scale, weights, etc.
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt
  
  teacher     User     @relation("TeacherGradeBooks", fields: [teacher_id], references: [id])
  categories  GradeCategory[]
  entries     GradeEntry[]
  students    GradeBookStudent[]
  
  @@index([teacher_id])
  @@index([course_code])
}

model GradeCategory {
  id           String   @id @default(uuid())
  gradebook_id String
  name         String
  weight       Float    @default(1.0)
  drop_lowest  Int      @default(0)
  description  String?
  color        String?
  created_at   DateTime @default(now())
  
  gradebook    GradeBook @relation(fields: [gradebook_id], references: [id], onDelete: Cascade)
  entries      GradeEntry[]
  
  @@unique([gradebook_id, name])
  @@index([gradebook_id])
}

model GradeEntry {
  id           String   @id @default(uuid())
  gradebook_id String
  category_id  String?
  name         String
  description  String?
  max_points   Float
  due_date     DateTime?
  assignment_id String? // Link to Assignment if applicable
  is_published Boolean  @default(false)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
  
  gradebook    GradeBook     @relation(fields: [gradebook_id], references: [id], onDelete: Cascade)
  category     GradeCategory? @relation(fields: [category_id], references: [id])
  grades       StudentGrade[]
  
  @@index([gradebook_id])
  @@index([category_id])
  @@index([assignment_id])
}

model StudentGrade {
  id         String   @id @default(uuid())
  entry_id   String
  student_id String
  points     Float?
  status     GradeStatus @default(NOT_SUBMITTED)
  feedback   String?
  graded_at  DateTime?
  late       Boolean  @default(false)
  excused    Boolean  @default(false)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  
  entry      GradeEntry @relation(fields: [entry_id], references: [id], onDelete: Cascade)
  student    User       @relation("StudentGrades", fields: [student_id], references: [id])
  
  @@unique([entry_id, student_id])
  @@index([student_id])
  @@index([status])
}

model GradeBookStudent {
  id           String   @id @default(uuid())
  gradebook_id String
  student_id   String
  enrolled_at  DateTime @default(now())
  dropped_at   DateTime?
  is_active    Boolean  @default(true)
  
  gradebook    GradeBook @relation(fields: [gradebook_id], references: [id], onDelete: Cascade)
  student      User      @relation("EnrolledGradeBooks", fields: [student_id], references: [id])
  
  @@unique([gradebook_id, student_id])
  @@index([student_id])
}

model LMSIntegration {
  id            String   @id @default(uuid())
  teacher_id    String
  lms_type      LMSType
  lms_course_id String
  gradebook_id  String?
  access_token  String   // Encrypted
  refresh_token String?  // Encrypted
  last_sync     DateTime?
  sync_enabled  Boolean  @default(true)
  settings      Json     // LMS-specific settings
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt
  
  teacher       User      @relation("LMSIntegrations", fields: [teacher_id], references: [id])
  gradebook     GradeBook? @relation(fields: [gradebook_id], references: [id])
  sync_logs     SyncLog[]
  
  @@unique([teacher_id, lms_type, lms_course_id])
  @@index([teacher_id])
}

model SyncLog {
  id             String   @id @default(uuid())
  integration_id String
  sync_type      SyncType
  status         SyncStatus
  records_synced Int      @default(0)
  errors         Json?
  started_at     DateTime @default(now())
  completed_at   DateTime?
  
  integration    LMSIntegration @relation(fields: [integration_id], references: [id], onDelete: Cascade)
  
  @@index([integration_id])
  @@index([status])
}

enum GradeStatus {
  NOT_SUBMITTED
  SUBMITTED
  GRADED
  LATE
  MISSING
  EXCUSED
}

enum LMSType {
  CANVAS
  MOODLE
  BLACKBOARD
  GOOGLE_CLASSROOM
  SCHOOLOGY
  BRIGHTSPACE
}

enum SyncType {
  GRADES_TO_LMS
  GRADES_FROM_LMS
  STUDENTS_FROM_LMS
  ASSIGNMENTS_FROM_LMS
  FULL_SYNC
}

enum SyncStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  PARTIAL
}
```

### Service Layer Implementation

#### Grade Book Service
```typescript
interface GradeBookService {
  createGradeBook(teacherId: string, gradeBook: CreateGradeBookRequest): Promise<GradeBook>
  updateGradeBook(gradeBookId: string, updates: UpdateGradeBookRequest): Promise<GradeBook>
  deleteGradeBook(gradeBookId: string): Promise<void>
  getGradeBookById(gradeBookId: string): Promise<GradeBookWithDetails>
  getTeacherGradeBooks(teacherId: string): Promise<GradeBook[]>
  
  // Category management
  addCategory(gradeBookId: string, category: CreateCategoryRequest): Promise<GradeCategory>
  updateCategory(categoryId: string, updates: UpdateCategoryRequest): Promise<GradeCategory>
  deleteCategory(categoryId: string): Promise<void>
  
  // Entry management
  addGradeEntry(gradeBookId: string, entry: CreateGradeEntryRequest): Promise<GradeEntry>
  updateGradeEntry(entryId: string, updates: UpdateGradeEntryRequest): Promise<GradeEntry>
  deleteGradeEntry(entryId: string): Promise<void>
  
  // Student management
  enrollStudent(gradeBookId: string, studentId: string): Promise<GradeBookStudent>
  unenrollStudent(gradeBookId: string, studentId: string): Promise<void>
  getGradeBookStudents(gradeBookId: string): Promise<User[]>
}

interface GradingService {
  enterGrade(entryId: string, studentId: string, grade: GradeInput): Promise<StudentGrade>
  updateGrade(gradeId: string, updates: GradeUpdate): Promise<StudentGrade>
  deleteGrade(gradeId: string): Promise<void>
  getStudentGrades(studentId: string, gradeBookId?: string): Promise<StudentGradeView[]>
  getEntryGrades(entryId: string): Promise<StudentGrade[]>
  calculateFinalGrade(studentId: string, gradeBookId: string): Promise<FinalGradeCalculation>
  
  // Bulk operations
  bulkEnterGrades(entryId: string, grades: BulkGradeInput[]): Promise<StudentGrade[]>
  importGradesFromCSV(entryId: string, csvData: string): Promise<ImportResult>
  exportGradesToCSV(gradeBookId: string): Promise<string>
}

interface LMSIntegrationService {
  setupIntegration(teacherId: string, integration: LMSIntegrationRequest): Promise<LMSIntegration>
  updateIntegration(integrationId: string, updates: LMSIntegrationUpdate): Promise<LMSIntegration>
  deleteIntegration(integrationId: string): Promise<void>
  getTeacherIntegrations(teacherId: string): Promise<LMSIntegration[]>
  
  // Sync operations
  syncGradesToLMS(integrationId: string, gradeBookId: string): Promise<SyncLog>
  syncGradesFromLMS(integrationId: string): Promise<SyncLog>
  syncStudentsFromLMS(integrationId: string): Promise<SyncLog>
  performFullSync(integrationId: string): Promise<SyncLog>
  
  // OAuth flow
  initiateOAuthFlow(teacherId: string, lmsType: LMSType): Promise<OAuthInitiation>
  completeOAuthFlow(teacherId: string, authCode: string, state: string): Promise<LMSIntegration>
  refreshAccessToken(integrationId: string): Promise<LMSIntegration>
}
```

### LMS Integration Adapters

#### Canvas Integration
```typescript
interface CanvasAdapter {
  authenticateUser(accessToken: string): Promise<CanvasUser>
  getCourses(accessToken: string): Promise<CanvasCourse[]>
  getAssignments(accessToken: string, courseId: string): Promise<CanvasAssignment[]>
  getStudents(accessToken: string, courseId: string): Promise<CanvasStudent[]>
  getGrades(accessToken: string, courseId: string, assignmentId: string): Promise<CanvasGrade[]>
  updateGrade(accessToken: string, courseId: string, assignmentId: string, studentId: string, grade: number): Promise<void>
  createAssignment(accessToken: string, courseId: string, assignment: CanvasAssignmentCreate): Promise<CanvasAssignment>
}

interface MoodleAdapter {
  authenticateUser(accessToken: string): Promise<MoodleUser>
  getCourses(accessToken: string): Promise<MoodleCourse[]>
  getGradebook(accessToken: string, courseId: string): Promise<MoodleGradebook>
  updateGrade(accessToken: string, gradeItemId: string, studentId: string, grade: number): Promise<void>
  getEnrolledUsers(accessToken: string, courseId: string): Promise<MoodleUser[]>
}

interface GoogleClassroomAdapter {
  authenticateUser(accessToken: string): Promise<ClassroomUser>
  getCourses(accessToken: string): Promise<ClassroomCourse[]>
  getCourseWork(accessToken: string, courseId: string): Promise<ClassroomCourseWork[]>
  getStudentSubmissions(accessToken: string, courseId: string, courseWorkId: string): Promise<ClassroomSubmission[]>
  updateGrade(accessToken: string, courseId: string, courseWorkId: string, submissionId: string, grade: number): Promise<void>
}
```

### Frontend Components

#### Grade Book Dashboard
```typescript
interface GradeBookDashboardProps {
  teacherId: string
  onGradeBookSelect: (gradeBook: GradeBook) => void
}

export function GradeBookDashboard({
  teacherId,
  onGradeBookSelect
}: GradeBookDashboardProps) {
  // Component implementation with:
  // - Grade book list
  // - Quick stats overview
  // - LMS integration status
  // - Recent activity feed
}
```

#### Grade Entry Grid
```typescript
interface GradeEntryGridProps {
  gradeBookId: string
  onGradeUpdate: (grade: StudentGrade) => void
}

export function GradeEntryGrid({
  gradeBookId,
  onGradeUpdate
}: GradeEntryGridProps) {
  // Component implementation with:
  // - Spreadsheet-like interface
  // - Inline editing
  // - Bulk operations
  // - Filtering and sorting
}
```

#### LMS Integration Setup
```typescript
interface LMSIntegrationSetupProps {
  teacherId: string
  onIntegrationComplete: (integration: LMSIntegration) => void
}

export function LMSIntegrationSetup({
  teacherId,
  onIntegrationComplete
}: LMSIntegrationSetupProps) {
  // Component implementation with:
  // - LMS selection
  // - OAuth flow handling
  // - Course mapping
  // - Sync preferences
}
```

#### Student Grade Portal
```typescript
interface StudentGradePortalProps {
  studentId: string
  gradeBookId?: string
}

export function StudentGradePortal({
  studentId,
  gradeBookId
}: StudentGradePortalProps) {
  // Component implementation with:
  // - Grade overview
  // - Progress tracking
  // - Assignment details
  // - Parent/guardian access
}
```

## Implementation Phases

### Phase 1: Core Grade Book (4 weeks)
1. **Database Schema Setup**
   - Create grade book models
   - Set up relationships and indexes
   - Create migration scripts

2. **Basic Grade Book Operations**
   - CRUD operations for grade books
   - Category and entry management
   - Student enrollment

3. **Grading Interface**
   - Grade entry and editing
   - Calculation algorithms
   - Basic reporting

### Phase 2: LMS Integration Framework (3 weeks)
1. **Integration Architecture**
   - OAuth flow implementation
   - Adapter pattern setup
   - Token management

2. **Canvas Integration**
   - Canvas API integration
   - Grade sync functionality
   - Student data sync

3. **Testing & Validation**
   - Integration testing
   - Error handling
   - Data validation

### Phase 3: Additional LMS Support (4 weeks)
1. **Google Classroom Integration**
   - Google API integration
   - Course work sync
   - Grade passback

2. **Moodle Integration**
   - Moodle web services
   - Grade book sync
   - User management

3. **Blackboard Integration**
   - Blackboard REST API
   - Grade center integration
   - Course data sync

### Phase 4: Advanced Features (3 weeks)
1. **Analytics & Reporting**
   - Grade analytics
   - Performance reports
   - Trend analysis

2. **Bulk Operations**
   - CSV import/export
   - Bulk grade entry
   - Mass updates

3. **Mobile Optimization**
   - Responsive design
   - Mobile-specific features
   - Offline capabilities

## Grade Calculation Algorithms

### Weighted Categories
```typescript
interface WeightedGradeCalculation {
  calculateCategoryGrade(grades: StudentGrade[], category: GradeCategory): number
  calculateFinalGrade(categoryGrades: CategoryGrade[], weights: CategoryWeight[]): number
  handleDropLowest(grades: StudentGrade[], dropCount: number): StudentGrade[]
  applyExtraCredit(baseGrade: number, extraCredit: number): number
}
```

### Standards-Based Grading
```typescript
interface StandardsBasedGrading {
  calculateProficiencyLevel(grades: StudentGrade[], standard: Standard): ProficiencyLevel
  aggregateStandardsGrades(standardGrades: StandardGrade[]): OverallGrade
  trackProgressOverTime(studentId: string, standardId: string): ProgressTimeline
}
```

### Curve and Scaling
```typescript
interface GradeCurving {
  applyCurve(grades: number[], curveType: CurveType): number[]
  calculateZScore(grade: number, mean: number, standardDeviation: number): number
  applyBellCurve(grades: number[], targetMean: number): number[]
}
```

## Security & Privacy

### Data Protection
- Encryption of sensitive grade data
- FERPA compliance measures
- Role-based access control
- Audit logging for all grade changes

### LMS Security
- Secure token storage
- Token refresh mechanisms
- API rate limiting
- Error handling without data exposure

## Analytics & Reporting

### Teacher Analytics
- Class performance overview
- Grade distribution analysis
- Assignment effectiveness metrics
- Student progress tracking

### Administrative Reports
- Course completion rates
- Grade trends over time
- Teacher workload analysis
- System usage statistics

### Student Analytics
- Individual progress reports
- Goal tracking
- Performance comparisons
- Improvement recommendations

## Success Criteria

### Integration Success
- 95% successful sync rate with LMS platforms
- Sub-5 second sync times for typical grade books
- Zero data loss during sync operations

### User Adoption
- 80% of teachers use grade book features regularly
- 90% satisfaction with grading interface
- 70% reduction in grading administrative time

### Technical Performance
- 99.9% uptime for grade book services
- Sub-200ms response times for grade queries
- Support for grade books with 1000+ students
