# SM-2+ Algorithm with Machine Learning - Development Plan

## Overview
Implement an enhanced SM-2 spaced repetition algorithm integrated with machine learning to optimize learning intervals based on user performance patterns and individual learning characteristics.

## Technical Architecture

### Core Components

#### 1. SM-2+ Algorithm Engine
- **Location**: `src/backend/services/sm2-plus.service.ts`
- **Purpose**: Enhanced SM-2 algorithm with ML integration
- **Dependencies**: 
  - Base SM-2 algorithm
  - ML prediction service
  - User performance analytics

#### 2. Machine Learning Integration
- **Location**: `src/backend/services/ml-prediction.service.ts`
- **Purpose**: ML models for interval optimization
- **Models**:
  - Forgetting curve prediction
  - Difficulty adjustment
  - Optimal interval calculation

#### 3. Performance Analytics
- **Location**: `src/backend/services/performance-analytics.service.ts`
- **Purpose**: Track and analyze user learning patterns
- **Metrics**:
  - Response time
  - Accuracy patterns
  - Retention rates
  - Learning velocity

## Database Schema Extensions

### New Tables

```prisma
model SM2PlusData {
  id                String   @id @default(uuid())
  user_id           String
  word_id           String
  easiness_factor   Float    @default(2.5)
  interval          Int      @default(1)
  repetition        Int      @default(0)
  quality           Int      // 0-5 scale
  response_time     Int      // milliseconds
  ml_adjustment     Float    @default(0.0)
  confidence_score  Float    @default(0.5)
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt
  
  user User @relation(fields: [user_id], references: [id])
  
  @@unique([user_id, word_id])
  @@index([user_id])
  @@index([word_id])
}

model LearningSession {
  id              String   @id @default(uuid())
  user_id         String
  session_start   DateTime @default(now())
  session_end     DateTime?
  words_reviewed  Int      @default(0)
  accuracy_rate   Float    @default(0.0)
  avg_response_time Int    @default(0)
  session_type    String   // 'review', 'new_words', 'practice'
  
  user User @relation(fields: [user_id], references: [id])
  
  @@index([user_id])
  @@index([session_start])
}

model MLModelMetrics {
  id              String   @id @default(uuid())
  model_version   String
  accuracy        Float
  precision       Float
  recall          Float
  f1_score        Float
  training_date   DateTime @default(now())
  is_active       Boolean  @default(false)
  
  @@index([model_version])
  @@index([is_active])
}
```

## Implementation Plan

### Phase 1: Core SM-2+ Algorithm (Week 1-2)

#### 1.1 Base Algorithm Implementation
```typescript
// src/backend/services/sm2-plus.service.ts
export interface SM2PlusService {
  calculateNextInterval(
    userId: string,
    wordId: string,
    quality: number,
    responseTime: number
  ): Promise<SM2PlusResult>;
  
  updateLearningData(
    userId: string,
    wordId: string,
    performance: PerformanceData
  ): Promise<void>;
  
  getOptimalReviewTime(
    userId: string,
    wordId: string
  ): Promise<Date>;
}

interface SM2PlusResult {
  nextInterval: number;
  easinessFactor: number;
  confidenceScore: number;
  mlAdjustment: number;
}
```

#### 1.2 Repository Layer
```typescript
// src/backend/repositories/sm2-plus.repository.ts
export interface SM2PlusRepository extends BaseRepository<SM2PlusData> {
  findByUserAndWord(userId: string, wordId: string): Promise<SM2PlusData | null>;
  updateLearningMetrics(data: SM2PlusUpdateData): Promise<SM2PlusData>;
  getUserLearningHistory(userId: string, limit?: number): Promise<SM2PlusData[]>;
  getPerformanceStats(userId: string, timeframe: TimeFrame): Promise<PerformanceStats>;
}
```

### Phase 2: Machine Learning Integration (Week 3-4)

#### 2.1 ML Model Development
- **Forgetting Curve Model**: Predict when user will forget a word
- **Difficulty Adjustment Model**: Adjust word difficulty based on user performance
- **Interval Optimization Model**: Optimize review intervals for maximum retention

#### 2.2 Feature Engineering
```typescript
interface MLFeatures {
  // User features
  totalWordsLearned: number;
  averageAccuracy: number;
  learningVelocity: number;
  sessionFrequency: number;
  
  // Word features
  wordDifficulty: number;
  wordFrequency: number;
  semanticComplexity: number;
  
  // Interaction features
  previousAccuracy: number;
  responseTimePattern: number[];
  retentionHistory: number[];
  intervalHistory: number[];
}
```

#### 2.3 Model Training Pipeline
```typescript
// src/backend/services/ml-training.service.ts
export interface MLTrainingService {
  trainForgettingCurveModel(trainingData: TrainingData[]): Promise<ModelMetrics>;
  trainDifficultyModel(trainingData: TrainingData[]): Promise<ModelMetrics>;
  trainIntervalModel(trainingData: TrainingData[]): Promise<ModelMetrics>;
  evaluateModel(modelId: string, testData: TestData[]): Promise<EvaluationResults>;
  deployModel(modelId: string): Promise<void>;
}
```

### Phase 3: Performance Analytics (Week 5-6)

#### 3.1 Analytics Service
```typescript
// src/backend/services/performance-analytics.service.ts
export interface PerformanceAnalyticsService {
  trackLearningSession(sessionData: LearningSessionData): Promise<void>;
  analyzeLearningPatterns(userId: string): Promise<LearningPatterns>;
  generatePerformanceReport(userId: string, period: TimePeriod): Promise<PerformanceReport>;
  identifyWeakAreas(userId: string): Promise<WeakArea[]>;
  predictLearningOutcome(userId: string, wordId: string): Promise<PredictionResult>;
}
```

#### 3.2 Real-time Analytics
- Track user interactions in real-time
- Update ML models with new data
- Provide immediate feedback on learning progress

### Phase 4: API Integration (Week 7)

#### 4.1 API Endpoints
```typescript
// src/backend/api/sm2-plus.ts
export async function getNextReviewWordsApi(
  userId: string,
  limit: number = 20
): Promise<WordReview[]>;

export async function submitWordReviewApi(
  userId: string,
  wordId: string,
  quality: number,
  responseTime: number
): Promise<ReviewResult>;

export async function getLearningAnalyticsApi(
  userId: string,
  timeframe: string
): Promise<LearningAnalytics>;
```

#### 4.2 Frontend Integration
```typescript
// src/hooks/use-sm2-plus.ts
export function useSM2Plus() {
  const [reviewWords, setReviewWords] = useState<WordReview[]>([]);
  const [analytics, setAnalytics] = useState<LearningAnalytics | null>(null);
  const [loading, setLoading] = useState(false);
  
  const getNextReviews = useCallback(async (limit: number) => {
    // Implementation
  }, []);
  
  const submitReview = useCallback(async (
    wordId: string,
    quality: number,
    responseTime: number
  ) => {
    // Implementation
  }, []);
  
  return {
    reviewWords,
    analytics,
    loading,
    getNextReviews,
    submitReview
  };
}
```

## ML Model Specifications

### 1. Forgetting Curve Model
- **Type**: Neural Network (LSTM/GRU)
- **Input**: User history, word features, time since last review
- **Output**: Probability of forgetting at time t
- **Training**: Historical review data with success/failure outcomes

### 2. Difficulty Adjustment Model
- **Type**: Gradient Boosting (XGBoost)
- **Input**: User performance, word characteristics, context
- **Output**: Optimal difficulty level (0.0-1.0)
- **Training**: User performance data with optimal difficulty labels

### 3. Interval Optimization Model
- **Type**: Reinforcement Learning (Q-Learning)
- **Input**: Current state (user, word, history)
- **Output**: Optimal next review interval
- **Training**: Reward based on retention success

## Performance Metrics

### Algorithm Metrics
- **Retention Rate**: Percentage of words retained after interval
- **Learning Efficiency**: Words learned per hour of study
- **Interval Accuracy**: How well predicted intervals match optimal intervals

### ML Model Metrics
- **Prediction Accuracy**: Accuracy of forgetting curve predictions
- **RMSE**: Root mean square error for interval predictions
- **AUC-ROC**: Area under curve for binary classification tasks

## Testing Strategy

### Unit Tests
- SM-2+ algorithm calculations
- ML model predictions
- Performance analytics calculations

### Integration Tests
- End-to-end learning flow
- ML model training pipeline
- API endpoint functionality

### A/B Testing
- Compare SM-2+ vs traditional SM-2
- Test different ML model configurations
- Evaluate user engagement metrics

## Deployment Strategy

### Model Deployment
- Use TensorFlow.js for client-side inference
- Fallback to server-side models for complex calculations
- Implement model versioning and rollback capabilities

### Monitoring
- Track model performance in production
- Monitor user engagement metrics
- Alert on model degradation

## Success Criteria

### Quantitative Metrics
- 15% improvement in retention rate vs traditional SM-2
- 20% reduction in study time for same learning outcomes
- 90%+ model prediction accuracy

### Qualitative Metrics
- Improved user satisfaction scores
- Reduced user churn rate
- Positive feedback on learning experience

## Timeline

- **Week 1-2**: Core SM-2+ algorithm implementation
- **Week 3-4**: ML model development and training
- **Week 5-6**: Performance analytics and monitoring
- **Week 7**: API integration and frontend implementation
- **Week 8**: Testing, optimization, and deployment

## Dependencies

### External Libraries
- TensorFlow.js for ML models
- scikit-learn for data preprocessing
- pandas/numpy for data manipulation

### Internal Dependencies
- User authentication system
- Word management system
- Collection management system
- Performance tracking infrastructure

## Risk Mitigation

### Technical Risks
- **Model Performance**: Implement fallback to traditional SM-2
- **Data Quality**: Implement data validation and cleaning
- **Scalability**: Use efficient algorithms and caching

### User Experience Risks
- **Learning Curve**: Provide clear explanations of algorithm benefits
- **Privacy Concerns**: Implement transparent data usage policies
- **Performance Impact**: Optimize for minimal latency
