# Kế Hoạch Triển <PERSON>hai Testing - Vocab Project

## Tổng Quan

Kế hoạch triển khai testing toàn diện cho ứng dụng học từ vựng Vocab, bao gồm unit testing, integration testing, end-to-end testing, và performance testing.

## Hiện Trạng Testing

### Tình Trạng Hiện Tại

-   ❌ **Chưa có framework testing chính thức**
-   ✅ **TypeScript compilation** cho type safety
-   ✅ **ESLint** cho code quality
-   ✅ **Health endpoints** cho monitoring
-   ✅ **Error handling** với comprehensive error management
-   ✅ **Manual testing** với error boundaries và graceful degradation

### Thách Thức Cần Giải Quyết

1. **Thiếu automated testing** cho business logic
2. **Không có integration tests** cho API endpoints
3. **Thiếu E2E testing** cho user workflows
4. **Không có performance testing** cho LLM operations
5. **Thiếu accessibility testing** automation

## Chiến <PERSON> Testing

### 1. Testing Pyramid

```
    /\     E2E Tests (10%)
   /  \    - Critical user journeys
  /____\   - Cross-browser testing
 /      \
/________\  Integration Tests (30%)
           - API endpoints
           - Database operations
           - LLM service integration

Unit Tests (60%)
- Services, repositories, utilities
- React components
- Custom hooks
```

### 2. Testing Framework Selection

#### Frontend Testing

-   **Jest** + **React Testing Library**: Component testing
-   **Vitest**: Fast unit testing với Vite compatibility
-   **MSW (Mock Service Worker)**: API mocking
-   **@testing-library/jest-dom**: DOM assertions

#### Backend Testing

-   **Jest**: Unit testing cho services và repositories
-   **Supertest**: API endpoint testing
-   **Prisma Test Environment**: Database testing
-   **Nock**: HTTP mocking cho external APIs

#### E2E Testing

-   **Playwright**: Cross-browser E2E testing
-   **Accessibility Testing**: axe-core integration

## Phase 1: Foundation Setup (Tuần 1-2)

### 1.1 Cài Đặt Dependencies

```bash
# Testing frameworks
yarn add -D jest @jest/globals
yarn add -D @testing-library/react @testing-library/jest-dom @testing-library/user-event
yarn add -D vitest @vitejs/plugin-react
yarn add -D msw
yarn add -D supertest @types/supertest
yarn add -D playwright @playwright/test
yarn add -D @axe-core/playwright

# Test utilities
yarn add -D jest-environment-jsdom
yarn add -D prisma-test-environment
yarn add -D nock
yarn add -D ts-jest
```

### 1.2 Configuration Files

#### Jest Configuration (`jest.config.js`)

```javascript
module.exports = {
	testEnvironment: 'jsdom',
	setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
	moduleNameMapping: {
		'^@/(.*)$': '<rootDir>/src/$1',
	},
	testMatch: [
		'<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
		'<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
	],
	collectCoverageFrom: ['src/**/*.{js,jsx,ts,tsx}', '!src/**/*.d.ts', '!src/test/**'],
	coverageThreshold: {
		global: {
			branches: 70,
			functions: 70,
			lines: 70,
			statements: 70,
		},
	},
};
```

#### Vitest Configuration (`vitest.config.ts`)

```typescript
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
	plugins: [react()],
	test: {
		environment: 'jsdom',
		setupFiles: ['./src/test/setup.ts'],
	},
	resolve: {
		alias: {
			'@': path.resolve(__dirname, './src'),
		},
	},
});
```

#### Playwright Configuration (`playwright.config.ts`)

```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
	testDir: './e2e',
	fullyParallel: true,
	forbidOnly: !!process.env.CI,
	retries: process.env.CI ? 2 : 0,
	workers: process.env.CI ? 1 : undefined,
	reporter: 'html',
	use: {
		baseURL: 'http://localhost:3000',
		trace: 'on-first-retry',
	},
	projects: [
		{
			name: 'chromium',
			use: { ...devices['Desktop Chrome'] },
		},
		{
			name: 'firefox',
			use: { ...devices['Desktop Firefox'] },
		},
		{
			name: 'webkit',
			use: { ...devices['Desktop Safari'] },
		},
		{
			name: 'Mobile Chrome',
			use: { ...devices['Pixel 5'] },
		},
	],
	webServer: {
		command: 'yarn dev',
		url: 'http://localhost:3000',
		reuseExistingServer: !process.env.CI,
	},
});
```

### 1.3 Test Setup Files

#### Test Setup (`src/test/setup.ts`)

```typescript
import '@testing-library/jest-dom';
import { server } from './mocks/server';

// MSW setup
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock Next.js router
jest.mock('next/navigation', () => ({
	useRouter: () => ({
		push: jest.fn(),
		replace: jest.fn(),
		back: jest.fn(),
	}),
	useSearchParams: () => new URLSearchParams(),
	usePathname: () => '/',
}));

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/vocab_test';
```

### 1.4 Package.json Scripts

```json
{
	"scripts": {
		"test": "jest",
		"test:watch": "jest --watch",
		"test:coverage": "jest --coverage",
		"test:unit": "vitest",
		"test:integration": "jest --testPathPattern=integration",
		"test:e2e": "playwright test",
		"test:e2e:ui": "playwright test --ui",
		"test:all": "yarn test && yarn test:e2e"
	}
}
```

## Phase 2: Unit Testing (Tuần 3-4)

### 2.1 Service Layer Testing

#### Example: Word Service Test

```typescript
// src/backend/services/__tests__/word.service.test.ts
import { WordServiceImpl } from '../word.service';
import { WordRepositoryImpl } from '../../repositories/word.repository';
import { mockWordRepository } from '../../test/mocks/repositories';

describe('WordService', () => {
	let wordService: WordServiceImpl;
	let mockRepository: jest.Mocked<WordRepositoryImpl>;

	beforeEach(() => {
		mockRepository = mockWordRepository();
		wordService = new WordServiceImpl(mockRepository);
	});

	describe('searchWords', () => {
		it('should return words matching search term', async () => {
			const mockWords = [
				{ id: '1', term: 'hello', language: 'EN' },
				{ id: '2', term: 'help', language: 'EN' },
			];
			mockRepository.searchWords.mockResolvedValue(mockWords);

			const result = await wordService.searchWords('hel', 'EN', 10);

			expect(result).toHaveLength(2);
			expect(mockRepository.searchWords).toHaveBeenCalledWith('hel', 'EN', 10);
		});

		it('should handle empty search term', async () => {
			mockRepository.find.mockResolvedValue([]);

			const result = await wordService.searchWords('', 'EN', 10);

			expect(result).toEqual([]);
			expect(mockRepository.find).toHaveBeenCalled();
		});
	});
});
```

### 2.2 Repository Layer Testing

#### Example: Collection Repository Test

```typescript
// src/backend/repositories/__tests__/collection.repository.test.ts
import { CollectionRepositoryImpl } from '../collection.repository';
import { PrismaClient } from '@prisma/client';
import { mockDeep, DeepMockProxy } from 'jest-mock-extended';

describe('CollectionRepository', () => {
	let repository: CollectionRepositoryImpl;
	let prismaMock: DeepMockProxy<PrismaClient>;

	beforeEach(() => {
		prismaMock = mockDeep<PrismaClient>();
		repository = new CollectionRepositoryImpl(prismaMock);
	});

	describe('findByUserId', () => {
		it('should return user collections', async () => {
			const mockCollections = [{ id: '1', name: 'Test Collection', user_id: 'user1' }];
			prismaMock.collection.findMany.mockResolvedValue(mockCollections);

			const result = await repository.findByUserId('user1');

			expect(result).toEqual(mockCollections);
			expect(prismaMock.collection.findMany).toHaveBeenCalledWith({
				where: { user_id: 'user1' },
			});
		});
	});
});
```

### 3.2 Database Integration Testing

#### Example: Prisma Integration Test

```typescript
// src/backend/repositories/__tests__/integration/collection.integration.test.ts
import { PrismaClient } from '@prisma/client';
import { CollectionRepositoryImpl } from '../../collection.repository';
import { setupTestDatabase, cleanupTestDatabase } from '../../../test/helpers/database';

describe('Collection Repository Integration', () => {
	let prisma: PrismaClient;
	let repository: CollectionRepositoryImpl;

	beforeAll(async () => {
		prisma = await setupTestDatabase();
		repository = new CollectionRepositoryImpl(prisma);
	});

	afterAll(async () => {
		await cleanupTestDatabase(prisma);
	});

	beforeEach(async () => {
		await prisma.collection.deleteMany();
		await prisma.user.deleteMany();
	});

	it('should create and retrieve collection', async () => {
		// Create test user
		const user = await prisma.user.create({
			data: {
				provider: 'USERNAME_PASSWORD',
				provider_id: 'test-user',
				username: 'testuser',
			},
		});

		// Create collection
		const collectionData = {
			name: 'Test Collection',
			target_language: 'EN' as const,
			source_language: 'VI' as const,
			user: { connect: { id: user.id } },
		};

		const created = await repository.create(collectionData);
		expect(created.id).toBeDefined();
		expect(created.name).toBe('Test Collection');

		// Retrieve collection
		const retrieved = await repository.findById(created.id);
		expect(retrieved).toEqual(created);
	});
});
```

### 3.3 LLM Service Integration Testing

#### Example: OpenAI Integration Test

```typescript
// src/backend/services/__tests__/integration/llm.integration.test.ts
import { LLMServiceImpl } from '../../llm.service';
import { CacheService } from '../../cache.service';
import nock from 'nock';

describe('LLM Service Integration', () => {
	let llmService: LLMServiceImpl;
	let cacheService: CacheService;

	beforeAll(() => {
		cacheService = new CacheService();
		llmService = new LLMServiceImpl(cacheService);
	});

	afterEach(() => {
		nock.cleanAll();
	});

	it('should generate vocabulary words', async () => {
		// Mock OpenAI API response
		nock('https://api.openai.com')
			.post('/v1/chat/completions')
			.reply(200, {
				choices: [
					{
						message: {
							content: JSON.stringify({
								words: [
									{
										term: 'algorithm',
										language: 'EN',
										definitions: [
											{
												pos: ['NOUN'],
												ipa: '/ˈælɡərɪðəm/',
												explains: [
													{
														EN: 'A process or set of rules',
														VI: 'Thuật toán',
													},
												],
												examples: [
													{
														EN: 'The algorithm solves the problem',
														VI: 'Thuật toán giải quyết vấn đề',
													},
												],
											},
										],
									},
								],
							}),
						},
					},
				],
				usage: { prompt_tokens: 100, completion_tokens: 200 },
			});

		const result = await llmService.generateRandomWords({
			keywords: ['technology'],
			language: 'EN',
			sourceLanguage: 'VI',
			count: 1,
			difficulty: 'INTERMEDIATE',
			excludeWordIds: [],
		});

		expect(result).toHaveLength(1);
		expect(result[0].term).toBe('algorithm');
		expect(result[0].definitions[0].explains[0].EN).toBe('A process or set of rules');
	});

	it('should handle API errors gracefully', async () => {
		nock('https://api.openai.com')
			.post('/v1/chat/completions')
			.reply(500, { error: 'Internal Server Error' });

		await expect(
			llmService.generateRandomWords({
				keywords: ['technology'],
				language: 'EN',
				sourceLanguage: 'VI',
				count: 1,
				difficulty: 'INTERMEDIATE',
				excludeWordIds: [],
			})
		).rejects.toThrow();
	});
});
```

## Phase 4: End-to-End Testing (Tuần 7-8)

### 4.1 Critical User Journeys

#### Example: Authentication Flow E2E Test

```typescript
// e2e/auth.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
	test('should login with username and password', async ({ page }) => {
		await page.goto('/login');

		// Fill login form
		await page.fill('[data-testid="username-input"]', 'testuser');
		await page.fill('[data-testid="password-input"]', 'testpassword');
		await page.click('[data-testid="login-button"]');

		// Should redirect to collections page
		await expect(page).toHaveURL('/collections');
		await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
	});

	test('should show error for invalid credentials', async ({ page }) => {
		await page.goto('/login');

		await page.fill('[data-testid="username-input"]', 'invalid');
		await page.fill('[data-testid="password-input"]', 'invalid');
		await page.click('[data-testid="login-button"]');

		await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
		await expect(page.locator('[data-testid="error-message"]')).toContainText(
			'Invalid credentials'
		);
	});
});
```

#### Example: Collection Management E2E Test

```typescript
// e2e/collections.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Collection Management', () => {
	test.beforeEach(async ({ page }) => {
		// Login before each test
		await page.goto('/login');
		await page.fill('[data-testid="username-input"]', 'testuser');
		await page.fill('[data-testid="password-input"]', 'testpassword');
		await page.click('[data-testid="login-button"]');
		await expect(page).toHaveURL('/collections');
	});

	test('should create new collection', async ({ page }) => {
		await page.click('[data-testid="create-collection-button"]');

		// Fill collection form
		await page.fill('[data-testid="collection-name-input"]', 'My Test Collection');
		await page.selectOption('[data-testid="target-language-select"]', 'EN');
		await page.selectOption('[data-testid="source-language-select"]', 'VI');
		await page.click('[data-testid="save-collection-button"]');

		// Should show success message and redirect
		await expect(page.locator('[data-testid="success-toast"]')).toBeVisible();
		await expect(page.locator('[data-testid="collection-card"]')).toContainText(
			'My Test Collection'
		);
	});

	test('should generate vocabulary words', async ({ page }) => {
		// Click on existing collection
		await page.click('[data-testid="collection-card"]:first-child');
		await page.click('[data-testid="vocabulary-tab"]');
		await page.click('[data-testid="generate-words-button"]');

		// Fill generation form
		await page.fill('[data-testid="keywords-input"]', 'technology, computer');
		await page.selectOption('[data-testid="difficulty-select"]', 'INTERMEDIATE');
		await page.fill('[data-testid="count-input"]', '5');
		await page.click('[data-testid="generate-button"]');

		// Should show loading and then results
		await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
		await expect(page.locator('[data-testid="word-card"]')).toHaveCount(5, { timeout: 30000 });
	});
});
```

### 4.2 Accessibility Testing

#### Example: Accessibility E2E Test

```typescript
// e2e/accessibility.spec.ts
import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('Accessibility', () => {
	test('should not have accessibility violations on home page', async ({ page }) => {
		await page.goto('/');

		const accessibilityScanResults = await new AxeBuilder({ page }).analyze();

		expect(accessibilityScanResults.violations).toEqual([]);
	});

	test('should support keyboard navigation', async ({ page }) => {
		await page.goto('/collections');

		// Test tab navigation
		await page.keyboard.press('Tab');
		await expect(page.locator(':focus')).toHaveAttribute(
			'data-testid',
			'create-collection-button'
		);

		await page.keyboard.press('Tab');
		await expect(page.locator(':focus')).toHaveAttribute('data-testid', 'collection-card');

		// Test Enter key activation
		await page.keyboard.press('Enter');
		await expect(page).toHaveURL(/\/collections\/[^\/]+$/);
	});

	test('should work with screen reader', async ({ page }) => {
		await page.goto('/collections');

		// Check ARIA labels
		await expect(page.locator('[data-testid="main-content"]')).toHaveAttribute('role', 'main');
		await expect(page.locator('[data-testid="collection-list"]')).toHaveAttribute('aria-label');
		await expect(page.locator('[data-testid="collection-card"]')).toHaveAttribute(
			'aria-describedby'
		);
	});
});
```

## Phase 5: Performance Testing (Tuần 9-10)

### 5.1 LLM Performance Testing

#### Example: LLM Load Test

```typescript
// src/test/performance/llm-load.test.ts
import { LLMServiceImpl } from '../../backend/services/llm.service';
import { performance } from 'perf_hooks';

describe('LLM Performance Tests', () => {
	let llmService: LLMServiceImpl;

	beforeAll(() => {
		llmService = new LLMServiceImpl(mockCacheService);
	});

	test('should handle concurrent word generation requests', async () => {
		const concurrentRequests = 10;
		const startTime = performance.now();

		const promises = Array.from({ length: concurrentRequests }, () =>
			llmService.generateRandomWords({
				keywords: ['technology'],
				language: 'EN',
				sourceLanguage: 'VI',
				count: 3,
				difficulty: 'INTERMEDIATE',
				excludeWordIds: [],
			})
		);

		const results = await Promise.all(promises);
		const endTime = performance.now();
		const totalTime = endTime - startTime;

		// All requests should complete
		expect(results).toHaveLength(concurrentRequests);
		results.forEach((result) => {
			expect(result).toHaveLength(3);
		});

		// Should complete within reasonable time (adjust based on requirements)
		expect(totalTime).toBeLessThan(30000); // 30 seconds
		console.log(`Concurrent requests completed in ${totalTime}ms`);
	});

	test('should respect token limits', async () => {
		const result = await llmService.generateRandomWords({
			keywords: ['technology', 'computer', 'software', 'programming'],
			language: 'EN',
			sourceLanguage: 'VI',
			count: 10,
			difficulty: 'ADVANCED',
			excludeWordIds: [],
		});

		// Should not exceed token limits
		expect(result.length).toBeLessThanOrEqual(10);

		// Check token usage (mock implementation)
		const tokenUsage = await llmService.getTokenUsage();
		expect(tokenUsage.totalTokens).toBeLessThan(3000);
	});
});
```

### 5.2 Database Performance Testing

#### Example: Database Load Test

```typescript
// src/test/performance/database-load.test.ts
import { CollectionServiceImpl } from '../../backend/services/collection.service';
import { performance } from 'perf_hooks';

describe('Database Performance Tests', () => {
	let collectionService: CollectionServiceImpl;

	beforeAll(async () => {
		collectionService = new CollectionServiceImpl(mockRepository);
	});

	test('should handle large collection queries efficiently', async () => {
		// Create test data
		const userId = 'test-user';
		const collections = Array.from({ length: 100 }, (_, i) => ({
			name: `Collection ${i}`,
			user_id: userId,
			word_ids: Array.from({ length: 50 }, () => `word-${Math.random()}`),
		}));

		// Insert test data
		await Promise.all(
			collections.map((collection) =>
				collectionService.createCollection(
					userId,
					collection.name,
					'EN',
					'VI',
					collection.word_ids
				)
			)
		);

		// Measure query performance
		const startTime = performance.now();
		const result = await collectionService.getUserCollections(userId);
		const endTime = performance.now();
		const queryTime = endTime - startTime;

		expect(result).toHaveLength(100);
		expect(queryTime).toBeLessThan(1000); // Should complete within 1 second
		console.log(`Large collection query completed in ${queryTime}ms`);
	});
});
```

## Phase 6: Test Automation & CI/CD (Tuần 11-12)

### 6.1 GitHub Actions Workflow

#### Example: Testing Workflow

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
    push:
        branches: [main, develop]
    pull_request:
        branches: [main]

jobs:
    unit-tests:
        runs-on: ubuntu-latest

        services:
            postgres:
                image: postgres:13
                env:
                    POSTGRES_PASSWORD: postgres
                    POSTGRES_DB: vocab_test
                options: >-
                    --health-cmd pg_isready
                    --health-interval 10s
                    --health-timeout 5s
                    --health-retries 5
                ports:
                    - 5432:5432

        steps:
            - uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '20'
                  cache: 'yarn'

            - name: Install dependencies
              run: yarn install --immutable

            - name: Run database migrations
              run: yarn prisma migrate deploy
              env:
                  DATABASE_URL: postgresql://postgres:postgres@localhost:5432/vocab_test

            - name: Run unit tests
              run: yarn test:coverage
              env:
                  DATABASE_URL: postgresql://postgres:postgres@localhost:5432/vocab_test

            - name: Upload coverage reports
              uses: codecov/codecov-action@v3

    integration-tests:
        runs-on: ubuntu-latest
        needs: unit-tests

        services:
            postgres:
                image: postgres:13
                env:
                    POSTGRES_PASSWORD: postgres
                    POSTGRES_DB: vocab_test
                options: >-
                    --health-cmd pg_isready
                    --health-interval 10s
                    --health-timeout 5s
                    --health-retries 5
                ports:
                    - 5432:5432

        steps:
            - uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '20'
                  cache: 'yarn'

            - name: Install dependencies
              run: yarn install --immutable

            - name: Run integration tests
              run: yarn test:integration
              env:
                  DATABASE_URL: postgresql://postgres:postgres@localhost:5432/vocab_test
                  LLM_OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY_TEST }}

    e2e-tests:
        runs-on: ubuntu-latest
        needs: integration-tests

        steps:
            - uses: actions/checkout@v4

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '20'
                  cache: 'yarn'

            - name: Install dependencies
              run: yarn install --immutable

            - name: Install Playwright browsers
              run: npx playwright install --with-deps

            - name: Run E2E tests
              run: yarn test:e2e
              env:
                  DATABASE_URL: ${{ secrets.DATABASE_URL_TEST }}

            - name: Upload Playwright report
              uses: actions/upload-artifact@v3
              if: always()
              with:
                  name: playwright-report
                  path: playwright-report/
```

### 6.2 Test Coverage Requirements

#### Coverage Thresholds

-   **Unit Tests**: 80% line coverage, 70% branch coverage
-   **Integration Tests**: 60% API endpoint coverage
-   **E2E Tests**: 100% critical user journey coverage

#### Coverage Reporting

```bash
# Generate coverage report
yarn test:coverage

# View coverage report
open coverage/lcov-report/index.html
```

### 6.3 Test Data Management

#### Test Database Setup

```typescript
// src/test/helpers/database.ts
import { PrismaClient } from '@prisma/client';

export async function setupTestDatabase(): Promise<PrismaClient> {
	const prisma = new PrismaClient({
		datasources: {
			db: {
				url: process.env.DATABASE_URL_TEST,
			},
		},
	});

	await prisma.$connect();

	// Run migrations
	await prisma.$executeRaw`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;

	return prisma;
}

export async function cleanupTestDatabase(prisma: PrismaClient): Promise<void> {
	await prisma.$transaction([
		prisma.collectionStats.deleteMany(),
		prisma.lastSeenWord.deleteMany(),
		prisma.feedback.deleteMany(),
		prisma.keyword.deleteMany(),
		prisma.collection.deleteMany(),
		prisma.multipleChoiceExercise.deleteMany(),
		prisma.paragraph.deleteMany(),
		prisma.example.deleteMany(),
		prisma.explain.deleteMany(),
		prisma.definition.deleteMany(),
		prisma.word.deleteMany(),
		prisma.user.deleteMany(),
	]);

	await prisma.$disconnect();
}
```

#### Test Fixtures

```typescript
// src/test/helpers/fixtures.ts
import { PrismaClient, Language, Difficulty } from '@prisma/client';

export async function createTestUser(prisma: PrismaClient, overrides = {}) {
	return prisma.user.create({
		data: {
			provider: 'USERNAME_PASSWORD',
			provider_id: `test-${Date.now()}`,
			username: `testuser-${Date.now()}`,
			...overrides,
		},
	});
}

export async function createTestCollection(prisma: PrismaClient, userId: string, overrides = {}) {
	return prisma.collection.create({
		data: {
			name: `Test Collection ${Date.now()}`,
			target_language: Language.EN,
			source_language: Language.VI,
			user_id: userId,
			...overrides,
		},
	});
}

export async function createTestWord(prisma: PrismaClient, overrides = {}) {
	return prisma.word.create({
		data: {
			term: `testword-${Date.now()}`,
			language: Language.EN,
			definitions: {
				create: {
					pos: ['NOUN'],
					ipa: '/test/',
					explains: {
						create: {
							EN: 'Test explanation',
							VI: 'Giải thích test',
						},
					},
					examples: {
						create: {
							EN: 'Test example',
							VI: 'Ví dụ test',
						},
					},
				},
			},
			...overrides,
		},
		include: {
			definitions: {
				include: {
					explains: true,
					examples: true,
				},
			},
		},
	});
}
```

## Phase 7: Monitoring & Maintenance (Tuần 13-14)

### 7.1 Test Monitoring Dashboard

#### Test Metrics Tracking

```typescript
// src/test/monitoring/test-metrics.ts
export interface TestMetrics {
	totalTests: number;
	passedTests: number;
	failedTests: number;
	skippedTests: number;
	coverage: {
		lines: number;
		branches: number;
		functions: number;
		statements: number;
	};
	performance: {
		averageTestTime: number;
		slowestTests: Array<{
			name: string;
			duration: number;
		}>;
	};
}

export class TestMetricsCollector {
	private metrics: TestMetrics = {
		totalTests: 0,
		passedTests: 0,
		failedTests: 0,
		skippedTests: 0,
		coverage: { lines: 0, branches: 0, functions: 0, statements: 0 },
		performance: { averageTestTime: 0, slowestTests: [] },
	};

	collectMetrics(testResults: any): void {
		// Collect test execution metrics
		this.metrics.totalTests = testResults.numTotalTests;
		this.metrics.passedTests = testResults.numPassedTests;
		this.metrics.failedTests = testResults.numFailedTests;
		this.metrics.skippedTests = testResults.numPendingTests;
	}

	generateReport(): string {
		return JSON.stringify(this.metrics, null, 2);
	}
}
```

### 7.2 Flaky Test Detection

#### Flaky Test Monitor

```typescript
// src/test/monitoring/flaky-test-detector.ts
export class FlakyTestDetector {
	private testHistory: Map<string, boolean[]> = new Map();

	recordTestResult(testName: string, passed: boolean): void {
		if (!this.testHistory.has(testName)) {
			this.testHistory.set(testName, []);
		}

		const history = this.testHistory.get(testName)!;
		history.push(passed);

		// Keep only last 10 runs
		if (history.length > 10) {
			history.shift();
		}
	}

	detectFlakyTests(): string[] {
		const flakyTests: string[] = [];

		for (const [testName, history] of this.testHistory) {
			if (history.length >= 5) {
				const passRate = history.filter((passed) => passed).length / history.length;

				// Consider test flaky if pass rate is between 20% and 80%
				if (passRate > 0.2 && passRate < 0.8) {
					flakyTests.push(testName);
				}
			}
		}

		return flakyTests;
	}
}
```

### 7.3 Test Maintenance Guidelines

#### Regular Maintenance Tasks

1. **Weekly**: Review test coverage reports and identify gaps
2. **Bi-weekly**: Update test data and fixtures
3. **Monthly**: Review and update E2E test scenarios
4. **Quarterly**: Performance test review and optimization

#### Test Debt Management

```typescript
// src/test/maintenance/test-debt-tracker.ts
export interface TestDebt {
	id: string;
	description: string;
	priority: 'low' | 'medium' | 'high' | 'critical';
	estimatedEffort: number; // hours
	createdAt: Date;
	assignee?: string;
}

export class TestDebtTracker {
	private debts: TestDebt[] = [];

	addDebt(debt: Omit<TestDebt, 'id' | 'createdAt'>): void {
		this.debts.push({
			...debt,
			id: `debt-${Date.now()}`,
			createdAt: new Date(),
		});
	}

	getHighPriorityDebts(): TestDebt[] {
		return this.debts
			.filter((debt) => debt.priority === 'high' || debt.priority === 'critical')
			.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
	}

	generateReport(): string {
		const totalDebts = this.debts.length;
		const totalEffort = this.debts.reduce((sum, debt) => sum + debt.estimatedEffort, 0);

		return `
Test Debt Report:
- Total debts: ${totalDebts}
- Total estimated effort: ${totalEffort} hours
- High priority debts: ${this.getHighPriorityDebts().length}
    `;
	}
}
```

## Kết Luận & Lộ Trình Triển Khai

### Timeline Tổng Thể (14 tuần)

| Phase       | Tuần  | Mục Tiêu                 | Deliverables                           |
| ----------- | ----- | ------------------------ | -------------------------------------- |
| **Phase 1** | 1-2   | Foundation Setup         | Test frameworks, configurations        |
| **Phase 2** | 3-4   | Unit Testing             | Service, repository, component tests   |
| **Phase 3** | 5-6   | Integration Testing      | API, database, LLM integration tests   |
| **Phase 4** | 7-8   | E2E Testing              | User journey, accessibility tests      |
| **Phase 5** | 9-10  | Performance Testing      | Load tests, performance benchmarks     |
| **Phase 6** | 11-12 | CI/CD Integration        | Automated testing pipeline             |
| **Phase 7** | 13-14 | Monitoring & Maintenance | Test monitoring, maintenance processes |

### Success Metrics

#### Quantitative Metrics

-   **Code Coverage**: ≥80% line coverage, ≥70% branch coverage
-   **Test Execution Time**: Unit tests <5 minutes, E2E tests <30 minutes
-   **Flaky Test Rate**: <5% of total tests
-   **Bug Detection Rate**: ≥90% of bugs caught by automated tests

#### Qualitative Metrics

-   **Developer Confidence**: Improved confidence in deployments
-   **Bug Reduction**: Reduced production bugs by 70%
-   **Development Speed**: Faster feature development with test safety net
-   **Code Quality**: Improved code maintainability and reliability

### Rủi Ro & Mitigation

#### Rủi Ro Chính

1. **LLM API Rate Limits**: Implement proper mocking and test data
2. **Database Performance**: Use test database with realistic data volumes
3. **Flaky E2E Tests**: Implement retry mechanisms and stable selectors
4. **Team Adoption**: Provide training and clear documentation

#### Mitigation Strategies

-   **Gradual Implementation**: Start with critical paths, expand gradually
-   **Team Training**: Regular workshops on testing best practices
-   **Tool Selection**: Choose stable, well-supported testing tools
-   **Continuous Monitoring**: Regular review of test effectiveness

### Next Steps

1. **Immediate (Tuần 1)**: Setup basic testing infrastructure
2. **Short-term (Tuần 2-4)**: Implement core unit tests
3. **Medium-term (Tuần 5-8)**: Add integration and E2E tests
4. **Long-term (Tuần 9-14)**: Performance testing and monitoring

Kế hoạch này cung cấp roadmap toàn diện để triển khai testing cho dự án Vocab, đảm bảo chất lượng code và reliability của ứng dụng học từ vựng.
