import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test('should load login page', async ({ page }) => {
    await page.goto('/login');
    
    // Check if the page loads
    await expect(page).toHaveTitle(/Vocab/);
    
    // Check for login form elements
    await expect(page.locator('input[type="text"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('should show validation errors for empty form', async ({ page }) => {
    await page.goto('/login');
    
    // Try to submit empty form
    await page.click('button[type="submit"]');
    
    // Should show validation errors
    await expect(page.locator('text=Username is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
  });

  test('should navigate to register page', async ({ page }) => {
    await page.goto('/login');
    
    // Click register link
    await page.click('text=Register');
    
    // Should navigate to register page
    await expect(page).toHaveURL(/\/register/);
  });

  test('should have working forgot password link', async ({ page }) => {
    await page.goto('/login');
    
    // Check if forgot password link exists
    await expect(page.locator('text=Forgot password?')).toBeVisible();
  });
});
